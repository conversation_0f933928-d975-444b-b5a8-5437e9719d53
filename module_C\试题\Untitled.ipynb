import os
from PIL import Image
import json
from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor
from qwen_vl_utils import process_vision_info
import torch

# 需要写绝对路径，以防之后路径出错
base_dir = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/药品信息'
resized_dir = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/药品信息/resized_512'
model_dir = "/home/<USER>/userSpace/studnet/Qwen2-VL-2B-Instruct"
os.makedirs(resized_dir, exist_ok=True)

model = Qwen2VLForConditionalGeneration.from_pretrained(model_dir, torch_dtype="auto", device_map="auto")
processor = AutoProcessor.from_pretrained(model_dir)

# <1> resize函数：将图片 resize 到合适大小 两处代码共1.5分
def resize_image(input_path, output_path, max_size=____):
    try:
        img = Image.open(input_path)
        _____________________________
        _____________________________
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        img.save(output_path, format='JPEG')
        return True
    except Exception as e:
        print(f"Failed to process {input_path}: {e}")
        return False

train_data = []

# <2> 读取子文件夹图片resize并找出药品正面图 两处代码共1分
for sub in os.listdir(base_dir):
    sub_path = os.path.join(base_dir, sub)
    if not os.path.isdir(sub_path):
        continue

    imgs = [fname for fname in sorted(os.listdir(sub_path))
            if fname.lower().endswith(('.jpeg'))]
    if not imgs:
        continue

    front_candidates = ________
    front_name = None
    for name in imgs:
        if name in front_candidates:
            front_name = name
            break
    if front_name is None:
        front_name = imgs[0]

    resized_paths_all = []
    resized_front_path = None

    for name in imgs:
        orig_path = os.path.join(sub_path, name)
        out_sub_dir = os.path.join(resized_dir, sub)
        os.makedirs(out_sub_dir, exist_ok=True)
        out_path = os.path.join(out_sub_dir, name)
        success = ________________________________
        if success:
            uri = f"file://{out_path}"
            resized_paths_all.append(uri)
            if name == front_name:
                resized_front_path = uri

    if resized_front_path is None:
        print(f"子文件夹 {sub} 未能找到并 resize 出正面图片，跳过")
        continue

# <3> 构造 messages，模型多图推理 4处代码共2分

    user_instruction = _________________________________________________________
    content = []
    for p in resized_paths_all:
        content.append(________________________________)
    content.append({"type": "text", "text": user_instruction})
    messages = [
        {
            "role": "user",
            "content": content
        }
    ]
    
    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
    image_inputs, video_inputs = process_vision_info(messages)
    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt"
    )
    inputs = inputs.to(model.device)
    
    with torch.no_grad():
        generated_ids = model.generate(**inputs, max_new_tokens=_____)
    
    generated_ids_trimmed = [
        out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
    ]
    output_texts = processor.batch_decode(
        generated_ids_trimmed,
        skip_special_tokens=True,
        clean_up_tokenization_spaces=False
    )
    answer = _______________________

# <4> 构造并保存训练集，3处代码共1.5分
    entry = {
        "conversations": [
            {"from": "human", "value": user_instruction},
            {"from": "gpt", "value": answer}
        ],
        "images": ____________________
    }
    train_data.append(entry)


output_json_path = _______________________
with open(output_json_path, 'w', encoding='utf-8') as f:
    json.dump(_______________________)

print(f"共处理 {len(train_data)} 个子文件夹，结果保存到 {output_json_path}")

# <5>  检查生成的药品信息，并在 LLaMA-Factory/data/dataset_info.json 中仿照已有数据格式补充填写新生成的json数据，并将新生成的 train.json 放入 LLaMA-Factory/data 中。共3分。
"drug_info": {
    "file_name": _____,
    "formatting": _____,
    "columns": {
        "messages": _____,
        "images": _____
    },
    "tags": {
        "role_tag": _____,
        "content_tag": _____,
        "user_tag": _____,
        "assistant_tag": _____
    }
}

# <1>  填充examples/train_lora/qwen2vl_lora_sft.yaml中相应参数，7处代码各0.5分，共3.5分。

### model
model_name_or_path: /home/<USER>/userSpace/studnet/Qwen2-VL-2B-Instruct

### method
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 8
lora_target: all

### dataset
dataset: _____
template: qwen2_vl
cutoff_len: 2048
max_samples: _____
overwrite_cache: true
preprocessing_num_workers: 16
dataloader_num_workers: 4

### output
output_dir: _____
logging_steps: 10
save_steps: 500
plot_loss: true
overwrite_output_dir: true
save_only_model: false
report_to: none  

### train
per_device_train_batch_size: ___
gradient_accumulation_steps: ___
learning_rate: ___
num_train_epochs: ___
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true
ddp_timeout: 180000000
resume_from_checkpoint: null

### eval
val_size: 0.1
per_device_eval_batch_size: 1
eval_strategy: steps
eval_steps: 500

# <2> 开始训练 llamafactory-cli train <your_train_yaml_path>, 0.5分

# <1> 确认 examples/merge_lora/llama3_lora_sft.yaml 中相应参数，进行模型导出。0.5分

### model
model_name_or_path: /home/<USER>/userSpace/studnet/Qwen2-VL-2B-Instruct
adapter_name_or_path: _____
template: qwen2_vl
trust_remote_code: true

### export
export_dir: ans/qwen2vl_lora_sft
export_size: 5
export_device: auto  
export_legacy_format: false

# <2> 开始导出 llamafactory-cli export <your_merge_yaml_path>，0.5分

# <1> 根据 Accuracy 及 规范输出数量 进行打分
# 相同分数情况下可比较规范输出数量

| Accuracy 范围         | 得分  |
|----------------------|-------|
| ≤ 0.60               | 0     |
| 0.60 < Accuracy ≤ 0.65 | 1     |
| 0.65 < Accuracy ≤ 0.70 | 1.5   |
| 0.70 < Accuracy ≤ 0.75 | 2     |
| 0.75 < Accuracy ≤ 0.80 | 2.5   |
| 0.80 < Accuracy ≤ 0.85 | 3     |
| > 0.85               | 3.5   |

# <1> 参考 "~/模块C/resource/rknn-llm/tree/main/examples/Qwen2-VL_Demo"文档进行模型转换和部署推理 共3分
# 模型转换（rknn、rkllm转换成功 1分）
# 模型编译 1分
# 模型部署、推理 1分

import numpy as np
import os
import torch
from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer
import torch.nn.functional as F

# 加载本地模型（加载预训练模型）
path = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/资源/LLaMA-Factory-0.9.1/ans/qwen2vl_lora_sft'
batch = 1
height = 392
width = 392
savepath = 'qwen2-vl-2b/qwen2_vl_2b_vision.onnx'

model = Qwen2VLForConditionalGeneration.from_pretrained(
    path,
    torch_dtype=torch.float32,
    low_cpu_mem_usage=True,
    trust_remote_code=True).eval()
tokenizer = AutoTokenizer.from_pretrained(path, trust_remote_code=True, use_fast=False)

N = batch                           # batch size
channel = 3                         # 3 for RGB
H = height                         # 图像高度
W = width                          # 图像宽度
merge_size = 2
temporal_patch_size = 2
patch_size = 14
grid_t = N // temporal_patch_size if N%temporal_patch_size == 0 else N // temporal_patch_size + 1
grid_h = H // patch_size
grid_w = W // patch_size

def export_onnx(image, step):
    # 根据batch大小调整输入图像的数量
    if N == 1:
        # <1>用repeat函数重复单张图片以匹配temporal_patch_size
        images = _____
    elif N % temporal_patch_size != 0:
        repeat_time = temporal_patch_size - N % temporal_patch_size
        repeat_image = image[-1:, ...].repeat(repeat_time, 1, 1, 1)
        images = torch.cat((image, repeat_image), dim=0)
    patches = images.reshape(grid_t, temporal_patch_size, channel, grid_h//merge_size, merge_size, patch_size, grid_w//merge_size, merge_size, patch_size)
    patches = patches.permute(0, 3, 6, 4, 7, 2, 1, 5, 8)
    flatten_patches = patches.reshape(grid_t * grid_h * grid_w, channel * temporal_patch_size * patch_size * patch_size)
    model.visual.forward = forward_new(model.visual)
    # 根据step参数判断是第一步还是第二步
    if step == 1:
        # <2>调用vision模块并传入flatten_patches和grid参数
        feature = _____
    else:
        feature = model.visual(flatten_patches)
    return feature

def forward_new(self):
    def tmp (hidden_states, grid_thw=None):
        hidden_states = self.patch_embed(hidden_states)
        if grid_thw is not None:
            rotary_pos_emb = self.rot_pos_emb(grid_thw)
            cu_seqlens = torch.repeat_interleave(grid_thw[:, 1] * grid_thw[:, 2], grid_thw[:, 0]).cumsum(
                dim=0, dtype=torch.int32
            )
            cu_seqlens = F.pad(cu_seqlens, (1, 0), value=0)
            np.save("./rotary_pos_emb.npy", rotary_pos_emb.cpu().detach().numpy())
            np.save("./cu_seqlens.npy", cu_seqlens.cpu().detach().numpy())
        else:
            rotary_pos_emb = torch.from_numpy(np.load("./rotary_pos_emb.npy")).to(dtype=hidden_states.dtype, device=hidden_states.device)
            cu_seqlens = torch.from_numpy(np.load("./cu_seqlens.npy")).to(dtype=torch.int32, device=hidden_states.device)
        
        for blk in self.blocks:
            hidden_states = blk(hidden_states, cu_seqlens=cu_seqlens, rotary_pos_emb=rotary_pos_emb)

        return self.merger(hidden_states)
    return tmp

# 导出 Vison 部分所对应的 onnx 模型，假设输入是2x3x392x392->(28x28)x(3x2x14x14)
# pixel_values = torch.randn(784, 1176, device="cuda", dtype=torch.float32)
pixel_values = torch.randn(N, channel, H, W, device="cpu", dtype=torch.float32)
model.forward = export_onnx
model = model.to(torch.float32).eval()

print("========================step1========================")
print("Generating the rotary_pos_emb and cu_seqlens done.")
feature = model(pixel_values, 1)

# 导出 Vison 部分所对应的 onnx 模型，假设输入是2x3x392x392->(28x28)x(3x2x14x14)
# pixel_values = torch.randn(784, 1176, device="cuda", dtype=torch.float32)
pixel_values = torch.randn(N, channel, H, W, device="cpu", dtype=torch.float32)
model.forward = export_onnx
model = model.to(torch.float32).eval()

print("========================step2========================")
print(f"Exporting the vision part of {path} to onnx format.")
os.makedirs(os.path.dirname(savepath), exist_ok=True)
# <3>执行第二步，并导出onnx模型，此处需要将opset_version设置为19
torch.onnx.export(_____)

from rknn.api import RKNN
import numpy as np
import os

# <3>填写转换出的onnx模型路径
model_path = _____
target_platform = 'rk3588'

rknn = RKNN(verbose=False)
rknn.config(target_platform=target_platform, mean_values=[[0.48145466 * 255, 0.4578275 * 255, 0.40821073 * 255]], std_values=[[0.26862954 * 255, 0.26130258 * 255, 0.27577711 * 255]])
rknn.load_onnx(model_path)
rknn.build(do_quantization=False, dataset=None)
os.makedirs("rknn", exist_ok=True)
rknn.export_rknn("./rknn/" + os.path.splitext(os.path.basename(model_path))[0] + "_{}.rknn".format(target_platform))


# <11> 每题0.5分 共2.5分