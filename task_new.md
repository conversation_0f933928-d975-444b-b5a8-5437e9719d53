# 人工智能工程技术职业技能竞赛任务说明

## 竞赛概述

本竞赛项目旨在考察选手在人工智能工程技术领域的综合应用能力，涵盖自然语言处理、计算机视觉和综合工程技术三个核心模块。竞赛采用实际工程项目的形式，要求选手完成从数据处理、模型构建、训练优化到部署应用的完整技术链条。

## 模块分布与分值

- **模块A：自然语言处理技术应用** (40分)
- **模块B：计算机视觉技术应用** (35分)  
- **模块C：综合工程技术应用** (25分)

## 模块A：自然语言处理技术应用 (40分)

### 技术概述
自然语言处理技术使计算机能够使用自然语言与人类进行有效通信，让计算机具有理解、处理、生成和模拟人类语言的能力。在医疗领域，NLP技术可以从海量医学文献和病历中提取关键信息，辅助诊断和治疗决策。

### 主要任务

#### 任务一：命名实体提取(NER) (21.5分)
**应用场景**：医学文本中的实体识别，如疾病名、药物名、症状等关键信息提取

**核心技术要点**：
- **数据格式转换**：JSON格式到BIOES标注格式的转换
- **序列标注模型**：CRF、BiLSTM、HMM、BiLSTM-CRF等经典模型
- **特征工程**：词向量、位置编码、转移概率矩阵
- **模型评估**：精确率、召回率、F1分数等指标

**技术难点**：
- BIOES标注体系的理解和实现
- 维特比算法的递推公式实现
- 条件随机场的概率计算

#### 任务二：三元组提取(KGC) (8.5分)
**应用场景**：医疗知识图谱构建，提取疾病-症状-药物等实体关系

**核心技术要点**：
- **大模型应用**：Few-shot学习和Prompt工程
- **知识图谱构建**：实体关系抽取和图结构构建
- **评估方法**：三元组匹配的精确率和召回率计算

### 数据形式
- 原始数据：医疗文本、标注信息(JSON格式)
- 处理后数据：BIOES格式标注、三元组结构
- 输出格式：结构化知识和实体关系

### 经典算法
- **序列标注**：BERT、BiLSTM、CRF、HMM
- **关系抽取**：基于规则、统计学习、深度学习方法
- **知识图谱**：实体链接、关系推理

## 模块B：计算机视觉技术应用 (35分)

### 技术概述
计算机视觉技术使计算机能对环境和其中的刺激进行可视化分析，让计算机具有自动提取、分析和理解视觉数据中有价值信息的能力。在医疗场景中，CV技术可实现疾病诊断、药品识别等关键应用。

### 主要任务

#### 任务一：病症分类 (17分)
**应用场景**：医学影像分析，如肺炎检测、病理图像分类

**核心技术要点**：
- **数据增强**：随机擦除(RandomErasing)、几何变换
- **深度学习模型**：ResNet18分类网络
- **损失函数**：交叉熵损失的实现和优化
- **性能可视化**：t-SNE降维、混淆矩阵分析

#### 任务二：药品检测 (18分)
**应用场景**：智能药房管理、药品安全监管

**核心技术要点**：
- **目标检测**：YOLOv8框架和Anchor-Free方法
- **数据增强**：Mosaic拼接技术
- **损失函数**：Distribution Focal Loss(DFL)
- **边缘部署**：模型转换和实际场景验证

### 数据形式
- 原始数据：医学影像(JPG)、标注框信息(YOLO格式)
- 增强数据：几何变换、颜色空间变换
- 输出格式：分类概率、检测框坐标

### 经典算法
- **分类网络**：ResNet、DenseNet、EfficientNet
- **目标检测**：YOLO系列、SSD、Faster R-CNN
- **数据增强**：AutoAugment、MixUp、CutMix

## 模块C：综合工程技术应用 (25分)

### 技术概述
综合工程技术将自然语言处理和计算机视觉技术相结合，利用多模态信息解决更加复杂、开放的现实问题。在医疗场景中，实现图文结合的智能问答和边缘设备部署。

### 主要任务

#### 任务一：药品知识问答 (17分)
**应用场景**：智能医疗咨询、药品信息查询系统

**核心技术要点**：
- **多模态模型**：视觉-语言联合理解
- **模型微调**：LoRA技术和参数高效训练
- **模型压缩**：AWQ量化技术(W4A16)
- **性能评估**：准确率评估和实际应用测试

#### 任务二：边缘设备部署 (8分)
**应用场景**：移动医疗设备、实时诊断系统

**核心技术要点**：
- **模型转换**：ONNX格式转换、RKNN编译
- **边缘优化**：模型量化、推理加速
- **实际验证**：真实场景测试和性能评估

### 数据形式
- 多模态数据：药品图像+文本描述
- 训练数据：图文对、问答对
- 部署格式：量化模型、边缘推理格式

### 经典算法
- **多模态模型**：CLIP、ViLT、Qwen2-VL
- **模型压缩**：量化、剪枝、知识蒸馏
- **边缘计算**：TensorRT、ONNX Runtime

## 技术发展趋势

### 自然语言处理
- 大语言模型的医疗领域适配
- 多语言医疗文本处理
- 知识图谱与LLM的融合

### 计算机视觉  
- 医学影像的多模态融合
- 少样本学习在医疗诊断中的应用
- 可解释AI在医疗场景的重要性

### 综合工程技术
- 端到端的多模态医疗AI系统
- 边缘计算在医疗设备中的普及
- 联邦学习保护医疗数据隐私

## 实际应用价值

本竞赛任务紧密结合医疗健康领域的实际需求，培养选手解决真实工程问题的能力：

1. **临床辅助诊断**：通过NLP和CV技术辅助医生进行疾病诊断
2. **智能药房管理**：实现药品的自动识别和管理
3. **医疗知识服务**：构建智能问答系统服务医患需求
4. **边缘医疗设备**：将AI能力部署到便携式医疗设备

通过完成这些任务，选手将掌握人工智能在医疗健康领域的核心应用技术，具备从算法研发到工程部署的全栈能力。
