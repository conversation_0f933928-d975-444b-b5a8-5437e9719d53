from sklearn_crfsuite import CRF
from .util import sent2features

class CRFModel(object):
    def __init__(self,
                 algorithm='lbfgs',
                 c1=0.1,
                 c2=0.1,
                 max_iterations=100,
                 all_possible_transitions=False
                 ):

        self.model = CRF(algorithm=algorithm,
                         c1=c1,
                         c2=c2,
                         max_iterations=max_iterations,
                         all_possible_transitions=all_possible_transitions)

    def train(self, sentences, tag_lists):
        # <1>提取句子特征
        features = _____
        # <1>训练模型
        self.model._____

    def test(self, sentences):
        # <2>提取句子特征 
        features = _____
        # <2>预测标签
        pred_tag_lists = self.model._____
        return pred_tag_lists