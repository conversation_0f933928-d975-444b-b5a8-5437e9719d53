import os
import json
import re
from PIL import Image
import torch
from sklearn.metrics import accuracy_score
from modelscope import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor

# 配置模型路径和测评文件夹路径
model_path = "/home/<USER>/userSpace/studnet/qwen2vl_lora_sft"
evaluation_folder = "/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/data"

processor = AutoProcessor.from_pretrained(model_path)
model = Qwen2VLForConditionalGeneration.from_pretrained(
    model_path,
    torch_dtype="auto",
    device_map="auto"
)
model.eval()

def load_json_files(folder_path):
    questions = []
    for filename in os.listdir(folder_path):
        if filename.endswith(".json") and filename != "ans.json":
            try:
                with open(os.path.join(folder_path, filename), "r", encoding="utf-8") as f:
                    data = json.load(f)
                if isinstance(data, list):
                    questions.extend([item for item in data if isinstance(item, dict)])
                elif isinstance(data, dict):
                    questions.append(data)
            except Exception as e:
                print(f"无法加载 JSON 文件 {filename}: {e}")
    return questions

def resize_image_keep_aspect_ratio(image, max_dim=512):
    w, h = image.size
    scale = max_dim / max(w, h)
    new_w = int(w * scale)
    new_h = int(h * scale)
    return image.resize((new_w, new_h), Image.BILINEAR)

def process_vision_images(image_paths):
    images = []
    for path in image_paths:
        try:
            img = Image.open(path).convert("RGB")
            img = resize_image_keep_aspect_ratio(img, 512)
            images.append(img)
        except Exception as e:
            print(f"无法打开图片 {path}：{e}")
    if not images:
        print(f"⚠️ 图片列表为空: {image_paths}")
    return images

def infer_answer(question_dict):
    question_text = question_dict.get("Q", "").strip()
    image_paths = question_dict.get("image", [])

    has_images = bool(image_paths)

    if has_images:
        messages = [
            {
                "role": "user",
                "content": (
                    [{"type": "image", "image": img_path} for img_path in image_paths] +
                    [{"type": "text", "text": question_text}]
                ),
            }
        ]
        pil_images = process_vision_images(image_paths)
    else:
        messages = [
            {
                "role": "user",
                "content": [{"type": "text", "text": question_text}],
            }
        ]
        pil_images = None

    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
    inputs = processor(text=[text], images=pil_images, padding=True, return_tensors="pt").to("cuda")

    with torch.no_grad():
        generated_ids = model.generate(**inputs, max_new_tokens=128)

    seq_len = inputs.input_ids.shape[-1]
    if generated_ids.shape[1] <= seq_len:
        print(f"[{question_text[:30]}...] 推理输出为空，跳过")
        return ""

    generated_ids_trimmed = generated_ids[:, seq_len:]
    output_list = processor.batch_decode(
        generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
    )
    if not output_list:
        print(f"[{question_text[:30]}...] 解码输出为空")
        return ""

    return output_list[0].strip()

def normalize_judge_output(output):
    text = output.strip()
    if text == "正确" or text == "错误":
        return text
    if "正确" in text:
        return "正确"
    if "错误" in text:
        return "错误"
    return "错误"  # 默认当成错误答案

def normalize_mc_output(output):
    text = output.strip()
    if re.match(r"^[A-D]$", text):
        return text
    m = re.search(r"\b([A-D])\b", text)
    if m:
        return m.group(1)
    return "Z"  # 非法答案统一为 Z

def evaluate_and_save_answers():
    questions = load_json_files(evaluation_folder)
    if not questions:
        print("未加载到任何题目。")
        return

    judge_labels, judge_preds = [], []
    mc_labels, mc_preds = [], []
    judge_non_standard_count = 0
    mc_non_standard_count = 0

    for idx, q in enumerate(questions):
        Q_text = q.get("Q", "")
        if not Q_text:
            print(f"[{idx}] 缺少 Q 字段，跳过。")
            continue
        try:
            raw_output = infer_answer(q)
        except Exception as e:
            print(f"[{idx}] 推理失败：{e}")
            raw_output = ""
        q["ans"] = raw_output
        correct_answer = q.get("A", "").strip()
        if correct_answer in ["正确", "错误"]:
            norm_pred = normalize_judge_output(raw_output)
            if norm_pred != raw_output:
                judge_non_standard_count += 1
            label = 1 if correct_answer == "正确" else 0
            pred = 1 if norm_pred == "正确" else 0
            judge_labels.append(label)
            judge_preds.append(pred)
        else:
            norm_pred = normalize_mc_output(raw_output)
            if norm_pred != raw_output:
                mc_non_standard_count += 1
            mc_labels.append(correct_answer)
            mc_preds.append(norm_pred)

    # 判断题评估
    acc_judge = accuracy_score(judge_labels, judge_preds)

    # 选择题评估（包含非法类 Z）
    acc_mc = accuracy_score(mc_labels, mc_preds)

    print("=== 判断题 评估结果 ===")
    print(f"样本数: {len(judge_labels)}")
    print(f"Accuracy: {acc_judge:.4f}")
    print(f"非规范输出数量: {judge_non_standard_count}")

    print("\n=== 选择题 评估结果 ===")
    print(f"样本数: {len(mc_labels)}")
    print(f"Accuracy: {acc_mc:.4f}")
    print(f"非规范输出数量: {mc_non_standard_count}")

    # 总体评估（保留所有样本）
    all_labels = (
        [label for label in judge_labels] +
        [{"A": 0, "B": 1, "C": 2, "D": 3, "Z": 4}.get(label, 4) for label in mc_labels]
    )
    all_preds = (
        [pred for pred in judge_preds] +
        [{"A": 0, "B": 1, "C": 2, "D": 3, "Z": 4}.get(pred, 4) for pred in mc_preds]
    )

    acc_all = accuracy_score(all_labels, all_preds)

    print("\n=== 总体评估结果（判断题 + 选择题） ===")
    print(f"总样本数: {len(all_labels)}")
    print(f"Accuracy: {acc_all:.4f}")

    try:
        ans_path = os.path.join(evaluation_folder, "ans.json")
        with open(ans_path, "w", encoding="utf-8") as f:
            json.dump(questions, f, ensure_ascii=False, indent=2)
        print(f"\n已保存输出结果到: {ans_path}")
    except Exception as e:
        print(f"保存 ans.json 时出错: {e}")


if __name__ == "__main__":
    evaluate_and_save_answers()
