# 人工智能工程技术竞赛评分体系

## 总体评分原则

本竞赛采用分层递进的评分体系，确保既能考察选手的基础技术能力，又能有效区分不同水平选手的综合实力。评分体系遵循"基础为主、能力区分"的原则，设置70%的基础内容考察工程技术水平，30%的进阶内容用于区分选手能力。

## 模块分值分配

| 模块 | 名称 | 分值 | 占比 |
|------|------|------|------|
| A | 自然语言处理技术应用 | 40.00分 | 40% |
| B | 计算机视觉技术应用 | 35.00分 | 35% |
| C | 综合工程技术应用 | 25.00分 | 25% |
| **总计** | | **100.00分** | **100%** |

## 基础内容评分标准 (70%分值)

### 分值区间设置
- **2分题目**：数据预处理步骤，包括格式转换、数据清洗等基础操作
- **3分题目**：模型训练步骤，包括损失计算、参数更新等核心流程
- **4分题目**：模型预测和简单可视化，包括结果输出和基础分析
- **5分题目**：模型构造和复杂功能实现，包括架构设计和高级特性

### 功能细项评分 (0.25-2分)
- **0.25分**：最小功能单元，如"重置图像大小"、"数据类型转换"等
- **0.5分**：简单功能实现，如"获取标签信息"、"基础参数设置"等  
- **1分**：标准功能实现，如"模型参数冻结"、"评估指标计算"等
- **2分**：复杂功能实现，如"完整forward函数"、"自定义损失函数"等

## 能力区分内容评分标准 (30%分值)

### 附加步骤 (2分)
主要为功能函数构造，要求选手具备：
- 根据任务需求设计算法的能力
- 理解复杂数学原理并实现的能力
- 解决实际工程问题的创新思维

### 附加功能 (1-2分)
- **1分功能**：给出提示且复杂度一般的完整功能
  - 示例：构造零样本分类器、实现特定评价指标
- **2分功能**：提示较少且复杂度较高的完整功能  
  - 示例：t-SNE可视化、自定义数据增强算法

## 各模块详细评分

### 模块A：自然语言处理技术 (40分)

#### A1：命名实体提取(NER)任务 (31.5分)
**基础理论考察 (10分)**
- 选择题5题，每题2分，考察NLP基础概念

**数据处理部分 (4分)**
- 文件格式转换 (1分)：JSON到BIOES格式转换
- BIOES标注生成 (1分)：实体标注规则实现  
- 文本分割处理 (1分)：句子边界识别
- 批量处理流程 (1分)：目录遍历和文件处理

**模型实现部分 (17.5分)**
- 数据构建 (1.5分)：词典构造和数据加载
- CRF模型 (2分)：特征提取和序列标注
- BiLSTM模型 (2.5分)：神经网络架构实现
- HMM模型 (4分)：概率矩阵估计和维特比算法
- BiLSTM-CRF模型 (4.5分)：混合模型和CRF层实现
- 模型训练测试 (3分)：完整训练流程和性能评估

#### A2：三元组提取(KGC)任务 (8.5分)
**数据处理 (2.5分)**
- 格式转换 (2分)：JSONL到TXT格式转换
- 路径配置 (0.5分)：文件路径参数设置

**大模型应用 (4分)**
- Few-shot示例构造 (1分)：示例数据格式设计
- Prompt提示词设计 (1.5分)：任务描述和输出格式规定
- 精度评测 (1.5分)：TP/FP/FN计算和指标评估

**知识图谱构建 (2分)**
- 图结构构建 (0.5分)：实体关系边添加
- 邻居查找 (1分)：基于关系类型的实体检索
- 路径查找 (0.5分)：最短路径算法实现

### 模块B：计算机视觉技术 (35分)

#### B1：病症分类任务 (17分)
**数据增强 (3分)**
- 随机擦除算法实现，包括概率判断、区域计算、像素替换

**数据预处理 (4.5分)**  
- 训练/测试集预处理设置 (1分)
- 数据增强操作定义 (1分)
- 数据集加载和预处理 (0.5分)
- 增广数据处理 (0.5分)
- DataLoader对象创建 (0.5分)

**模型构建 (6.5分)**
- 损失函数构造 (1.5分)：交叉熵损失实现
- 分类模型设置 (2分)：ResNet18配置和优化器选择
- 训练验证函数 (3分)：完整训练循环和性能评估

**结果分析 (6分)**
- 测试结果可视化 (3分)：预测结果展示
- 性能可视化 (3分)：t-SNE降维和特征分析

#### B2：药品检测任务 (18分)
**数据增强 (2分)**
- Mosaic拼接算法实现，包括画布初始化和图像拼接

**数据预处理 (1分)**
- 预处理流程整合

**数据获取标注 (2分)**
- 摄像头图像获取 (1分)
- 数据标注平台使用 (1分)

**模型构建 (4分)**
- YOLOv8架构配置 (2分)：backbone和head结构设计
- DFL损失函数 (2分)：Distribution Focal Loss实现

**训练部署 (9分)**
- 模型训练预测 (2.25分)：训练流程和配置文件
- 结果可视化 (0.75分)：检测结果展示
- 平台展示 (3分)：硬件平台集成和结果展示

### 模块C：综合工程技术 (25分)

#### C1：药品知识问答 (17分)
**数据准备 (8.5分)**
- 图像预处理 (1.5分)：resize函数和图像处理
- 正面图识别 (1分)：图像筛选和处理
- Prompt构造 (2分)：指令设计和消息结构
- 数据集构建 (2分)：训练数据生成和保存
- 配置文件 (2分)：dataset_info.json配置

**模型训练 (4分)**
- YAML配置 (3.5分)：训练参数设置
- 训练执行 (0.5分)：模型训练过程

**模型导出 (1分)**
- 导出配置和执行 (1分)

**性能评估 (3.5分)**
- 准确率评分：根据模型准确率分档评分
  - Accuracy > 0.85: 3.5分
  - 0.80 < Accuracy ≤ 0.85: 3分
  - 0.75 < Accuracy ≤ 0.80: 2.5分
  - 0.70 < Accuracy ≤ 0.75: 2分
  - 0.65 < Accuracy ≤ 0.70: 1.5分
  - 0.60 < Accuracy ≤ 0.65: 1分
  - Accuracy ≤ 0.60: 0分

#### C2：边缘设备部署 (8分)
**模型转换 (4分)**
- ONNX模型转换 (1.5分)
- RKNN模型转换 (0.5分)  
- RKLLM模型转换 (1分)
- 模型部署配置 (1分)

**边缘测试 (2.5分)**
- 板端测试：5题×0.5分

## 评分细则说明

### 代码实现评分
- **完全正确**：获得满分
- **部分正确**：按功能点比例给分
- **逻辑错误**：根据错误严重程度扣分
- **未实现**：该项得0分

### 实验结果评分
- **结果正确且分析合理**：获得满分
- **结果正确但分析不足**：扣除20%-30%分值
- **结果部分正确**：按正确比例给分
- **结果错误**：该项得0分

### 创新加分机制
对于在基础要求之上展现创新思维和优秀工程实践的选手，可给予额外加分：
- **算法优化**：提出有效的性能优化方案
- **工程创新**：展现优秀的系统设计能力
- **应用拓展**：结合实际需求的功能扩展

## 质量保障措施

### 评分标准化
- 制定详细的评分细则和示例
- 建立评分员培训和认证机制
- 实施多轮评分和交叉验证

### 公平性保障
- 匿名评分机制
- 多评分员独立评分
- 争议处理和申诉机制

### 技术验证
- 代码自动化测试
- 模型性能基准验证
- 实际部署效果检验

通过科学合理的评分体系，确保竞赛能够准确评估选手的技术水平，促进人工智能工程技术人才的培养和选拔。
