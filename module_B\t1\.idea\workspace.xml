<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="f89e1818-b622-4255-bd61-967398fee86b" name="Default Changelist" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf>
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/classification_full.ipynb">
          <provider selected="true" editor-type-id="text-editor" />
        </entry>
      </file>
    </leaf>
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="580" />
    <option name="y" value="200" />
    <option name="width" value="1400" />
    <option name="height" value="1000" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="t1" type="b2602c69:ProjectViewProjectNode" />
              <item name="t1" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="last_opened_file_path" value="$PROJECT_DIR$/../t2" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f89e1818-b622-4255-bd61-967398fee86b" name="Default Changelist" comment="" />
      <created>1756969750166</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756969750166</updated>
    </task>
    <servers />
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="2576" height="1416" extended-state="6" />
    <layout>
      <window_info content_ui="combo" id="Project" order="0" visible="true" weight="0.08747045" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Favorites" order="2" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Version Control" order="7" />
      <window_info active="true" anchor="bottom" id="Terminal" order="8" visible="true" weight="0.21901792" />
      <window_info anchor="bottom" id="Event Log" order="9" side_tool="true" />
      <window_info anchor="bottom" id="Python Console" order="10" weight="0.32969603" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/classification_full.ipynb">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
  </component>
</project>