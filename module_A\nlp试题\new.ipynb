{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# 自然语言处理技术应用 - 模块A\n", "\n", "本文件整合了命名实体提取（NER）和三元组提取（KGC）两个任务的完整实现。\n", "\n", "## 任务一：命名实体提取（NER）\n", "\n", "### 概述\n", "命名实体提取是从文本中识别和分类命名实体（如人名、地名、机构名等）的任务。本任务包含数据处理和多种模型实现。"]}, {"cell_type": "markdown", "id": "setup-imports", "metadata": {}, "source": ["### 导入必要的库"]}, {"cell_type": "code", "execution_count": null, "id": "imports", "metadata": {}, "outputs": [], "source": ["# 基础库\n", "import os\n", "import json\n", "import time\n", "import ast\n", "from typing import List, Dict, Any, Tuple\n", "from collections import Counter, defaultdict\n", "from codecs import open\n", "from os.path import join\n", "from itertools import zip_longest\n", "from copy import deepcopy\n", "\n", "# 机器学习库\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.nn.utils.rnn import pad_packed_sequence, pack_padded_sequence\n", "\n", "# 可选库（如果环境中有的话）\n", "try:\n", "    from sklearn_crfsuite import CRF\n", "    CRF_AVAILABLE = True\n", "except ImportError:\n", "    print(\"sklearn_crfsuite 未安装，CRF模型将不可用\")\n", "    CRF_AVAILABLE = False\n", "\n", "try:\n", "    from modelscope import AutoModelForCausalLM, AutoTokenizer\n", "    MODELSCOPE_AVAILABLE = True\n", "except ImportError:\n", "    print(\"modelscope 未安装，大模型功能将不可用\")\n", "    MODELSCOPE_AVAILABLE = False\n", "\n", "try:\n", "    import networkx as nx\n", "    NETWORKX_AVAILABLE = True\n", "except ImportError:\n", "    print(\"networkx 未安装，知识图谱功能将不可用\")\n", "    NETWORKX_AVAILABLE = False\n", "\n", "from tqdm import tqdm\n", "\n", "print(\"库导入完成！\")"]}, {"cell_type": "markdown", "id": "ner-data-processing", "metadata": {}, "source": ["## 1. 数据处理（convert2bioes.py）\n", "\n", "将JSON格式的标注数据转换为BIOES格式，用于训练序列标注模型。"]}, {"cell_type": "markdown", "id": "step1-split", "metadata": {}, "source": ["### 步骤1：文本分割函数"]}, {"cell_type": "code", "execution_count": null, "id": "split-sentences", "metadata": {}, "outputs": [], "source": ["def split_sentences(text: str) -> List[str]:\n", "    \"\"\"\n", "    根据特定标点符号分割文本为句子列表\n", "    \n", "    Args:\n", "        text: 待分割的文本\n", "    \n", "    Returns:\n", "        分割后的句子列表\n", "    \"\"\"\n", "    sentence_endings = \"。！？\"\n", "    sentences = []\n", "    start = 0\n", "    #---------------------------------------1------------------------------------------\n", "    # TODO: 实现文本分割逻辑\n", "    # 遍历文本字符，当遇到句子结束符时，将从start到当前位置的文本作为一个句子\n", "    # 需要：1. 添加句子到sentences列表 2. 更新start位置 3. 处理剩余文本\n", "    for i in range(len(text)):\n", "        if text[i] in sentence_endings:\n", "            sentences._____\n", "            start = _____\n", "    if start < len(text):\n", "        sentences._____\n", "    #---------------------------------------1------------------------------------------\n", "    return sentences"]}, {"cell_type": "markdown", "id": "step2-entity", "metadata": {}, "source": ["### 步骤2：实体处理函数"]}, {"cell_type": "code", "execution_count": null, "id": "process-entity", "metadata": {}, "outputs": [], "source": ["def process_entity(\n", "    sentence: str, \n", "    sent_text: str, \n", "    entity: Dict[str, Any],\n", "    tags: List[str]\n", ") -> None:\n", "    \"\"\"\n", "    处理单个实体，生成对应的BIOES标签\n", "    \n", "    Args:\n", "        sentence: 当前处理的句子\n", "        sent_text: 包含实体的完整文本\n", "        entity: 实体信息字典\n", "        tags: 标签列表，将被修改\n", "        B: 实体的开始\n", "        E: 实体的结束\n", "        I: 实体的内部\n", "        S: 单字符实体\n", "        O: 非实体\n", "    \"\"\"\n", "    start_idx = entity.get('start_idx', 0) - sent_text.index(sentence)\n", "    end_idx = entity.get('end_idx', 0) - sent_text.index(sentence)\n", "    entity_type = entity.get('entity_type', '')\n", "    \n", "    # 检查实体索引是否在当前句子范围内\n", "    if start_idx < 0 or end_idx > len(sentence):\n", "        return\n", "    #------------------------------------2------------------------------\n", "    # TODO: 根据实体长度设置BIOES标签\n", "    # 单字实体使用S标签，多字实体使用B-I-E标签组合\n", "    # 标签格式：标签类型-实体类型，如'S-PER'、'B-ORG'等\n", "    if start_idx == end_idx - 1:\n", "        # 单字实体\n", "        tags[start_idx] = _____\n", "    else:\n", "        # 多字实体\n", "        tags[start_idx] = _____\n", "        for i in range(start_idx + 1, end_idx - 1):\n", "            tags[i] = _____\n", "        tags[end_idx - 1] = _____\n", "    #------------------------------------2------------------------------"]}, {"cell_type": "markdown", "id": "step3-convert", "metadata": {}, "source": ["### 步骤3：BIOES格式转换函数"]}, {"cell_type": "code", "execution_count": null, "id": "convert-bioes", "metadata": {}, "outputs": [], "source": ["def convert_to_bioes(json_data: Dict[str, Any]) -> List[str]:\n", "    \"\"\"\n", "    将JSON格式的数据转换为BIOES标注格式\n", "    \n", "    Args:\n", "        json_data: 包含段落和实体信息的JSON数据\n", "    \n", "    Returns:\n", "        BIOES格式的行列表\n", "    \"\"\"\n", "    bioes_lines = []\n", "    \n", "    for paragraph in json_data.get('paragraphs', []):\n", "        paragraph_text = paragraph.get('paragraph', '')\n", "        sentences = split_sentences(paragraph_text)\n", "        \n", "        for sentence in sentences:\n", "            # 初始化标签列表\n", "            tags = ['O'] * len(sentence)\n", "            #------------------------------------3------------------------------\n", "            # TODO: 遍历段落中的句子信息，处理实体标注\n", "            # 需要：1. 获取句子信息 2. 匹配当前句子 3. 处理句子中的实体\n", "            for sent_info in paragraph.get('sentences', []):\n", "                sent_text = sent_info.get('sentence', '')\n", "                if sentence in sent_text:\n", "                    entities = sent_info.get('entities', [])\n", "                    for entity in entities:\n", "                        _____\n", "            #------------------------------------3------------------------------\n", "            # 生成BIOES格式的行，忽略空格、回车和制表符\n", "            for char, tag in zip(sentence, tags):\n", "                if char.strip():\n", "                    bioes_lines.append(f'{char} {tag}')\n", "            bioes_lines.append('')  # 句子之间用空行隔开\n", "            \n", "    \n", "    return bioes_lines"]}, {"cell_type": "markdown", "id": "step4-process-dir", "metadata": {}, "source": ["### 步骤4：目录处理函数"]}, {"cell_type": "code", "execution_count": null, "id": "process-directory", "metadata": {}, "outputs": [], "source": ["def process_directory(input_dir: str) -> List[str]:\n", "    \"\"\"\n", "    处理目录中的所有JSON文件，转换为BIOES格式\n", "    \n", "    Args:\n", "        input_dir: 输入目录路径\n", "    \n", "    Returns:\n", "        所有文件的BIOES格式行列表\n", "    \"\"\"\n", "    all_bioes_lines = []\n", "    \n", "    # 遍历目录中的所有JSON文件\n", "    for filename in os.listdir(input_dir):\n", "        if filename.endswith('.json'):\n", "            file_path = os.path.join(input_dir, filename)\n", "            try:#----------------------------------------------4--------------------------------------\n", "                # TODO: 处理JSON文件并转换为BIOES格式\n", "                # 需要：1. 加载JSON数据 2. 转换为BIOES格式 3. 添加到总列表\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    json_data = json._____\n", "                    bioes_lines = _____\n", "                    all_bioes_lines._____\n", "                #----------------------------------------------4--------------------------------------\n", "            except Exception as e:\n", "                print(f\"处理文件 {filename} 时出错: {e}\")\n", "    \n", "    return all_bioes_lines"]}, {"cell_type": "markdown", "id": "ner-models", "metadata": {}, "source": ["## 2. 命名实体识别模型实现\n", "\n", "本部分实现多种序列标注模型，包括HMM、CRF、BiLSTM和BiLSTM-CRF。"]}, {"cell_type": "markdown", "id": "data-utils", "metadata": {}, "source": ["### 数据处理工具函数"]}, {"cell_type": "code", "execution_count": null, "id": "data-processing", "metadata": {}, "outputs": [], "source": ["def build_map(lists):\n", "    \"\"\"构建词汇到ID的映射\"\"\"\n", "    maps = {}\n", "    for list_ in lists:\n", "        for e in list_:\n", "            if e not in maps:\n", "                maps[e] = len(maps)\n", "    return maps\n", "\n", "def build_corpus(split, make_vocab=True, data_dir=\"./data\"):\n", "    \"\"\"读取数据\"\"\"\n", "    assert split in ['train', 'dev', 'test']\n", "\n", "    word_lists = []\n", "    tag_lists = []\n", "    with open(join(data_dir, split+\".bioes\"), 'r', encoding='utf-8') as f:\n", "        word_list = []\n", "        tag_list = []\n", "        #---------------------------------------1----------------------------------------\n", "        # TODO: 读取BIOES格式文件并解析词和标签\n", "        # 需要：1. 分割每行的词和标签 2. 添加到列表 3. 处理句子边界\n", "        for line in f:\n", "            if line != '\\n':\n", "                word, tag = _____\n", "                word_list.append(word)\n", "                tag_list.append(tag)\n", "            elif (word_list!= []) and (tag_list!= [] ):\n", "                word_lists._____\n", "                tag_lists._____\n", "                word_list = []\n", "                tag_list = []\n", "        #---------------------------------------1----------------------------------------\n", "\n", "    #------------------------------------------2-----------------------------------\n", "    # TODO: 构建词汇表和标签映射\n", "    # 需要：1. 构建word2id映射 2. 构建tag2id映射\n", "    # 如果make_vocab为True，还需要返回word2id和tag2id\n", "    if make_vocab:\n", "        word2id = _____\n", "        tag2id = _____\n", "        return word_lists, tag_lists, word2id, tag2id\n", "    else:\n", "        return word_lists, tag_lists\n", "    #------------------------------------------2-----------------------------------"]}, {"cell_type": "markdown", "id": "utility-functions", "metadata": {}, "source": ["### 工具函数"]}, {"cell_type": "code", "execution_count": null, "id": "utils", "metadata": {}, "outputs": [], "source": ["def flatten_lists(lists):\n", "    \"\"\"将嵌套列表展平\"\"\"\n", "    return [item for sublist in lists for item in sublist]\n", "\n", "def save_model(model, file_name):\n", "    \"\"\"保存模型\"\"\"\n", "    import pickle\n", "    with open(file_name, 'wb') as f:\n", "        pickle.dump(model, f)\n", "    print(f\"模型已保存到 {file_name}\")\n", "\n", "def tensorized(batch, maps):\n", "    \"\"\"将批次数据转换为张量\"\"\"\n", "    PAD = maps.get('<pad>', 0)\n", "    UNK = maps.get('<unk>', 1)\n", "    \n", "    max_len = max([len(s) for s in batch])\n", "    batch_size = len(batch)\n", "    \n", "    batch_tensor = torch.ones(batch_size, max_len).long() * PAD\n", "    lengths = []\n", "    \n", "    for i, sent in enumerate(batch):\n", "        lengths.append(len(sent))\n", "        for j, word in enumerate(sent):\n", "            batch_tensor[i][j] = maps.get(word, UNK)\n", "    \n", "    return batch_tensor, lengths\n", "\n", "def sort_by_lengths(word_lists, tag_lists):\n", "    \"\"\"按长度排序\"\"\"\n", "    pairs = list(zip(word_lists, tag_lists))\n", "    indices = sorted(range(len(pairs)), key=lambda k: len(pairs[k][0]), reverse=True)\n", "    pairs = [pairs[i] for i in indices]\n", "    word_lists, tag_lists = list(zip(*pairs))\n", "    return word_lists, tag_lists, indices"]}, {"cell_type": "markdown", "id": "hmm-model", "metadata": {}, "source": ["### HMM模型实现\n", "\n", "隐马尔可夫模型（HMM）是一种经典的序列标注模型。"]}, {"cell_type": "code", "execution_count": null, "id": "hmm-class", "metadata": {}, "outputs": [], "source": ["class HMM(object):\n", "    def __init__(self, N, M):\n", "        \"\"\"Args:\n", "            N: 状态数，这里对应存在的标注的种类\n", "            M: 观测数，这里对应有多少不同的字\n", "        \"\"\"\n", "        self.N = N\n", "        self.M = M\n", "\n", "        # 状态转移概率矩阵 A[i][j]表示从i状态转移到j状态的概率\n", "        self.A = torch.zeros(N, N)\n", "        # 观测概率矩阵, B[i][j]表示i状态下生成j观测的概率\n", "        self.B = torch.zeros(N, M)\n", "        # 初始状态概率  Pi[i]表示初始时刻为状态i的概率\n", "        self.Pi = torch.zeros(N)\n", "\n", "    def train(self, word_lists, tag_lists, word2id, tag2id):\n", "        \"\"\"HMM的训练，即根据训练语料对模型参数进行估计\"\"\"\n", "        assert len(tag_lists) == len(word_lists)\n", "\n", "        # 估计转移概率矩阵\n", "        #-----------------------------------------------1--------------------------------------------\n", "        # TODO: 估计状态转移概率矩阵\n", "        # 需要：1. 获取当前和下一个标签ID 2. 更新转移计数 3. 归一化\n", "        for tag_list in tag_lists:\n", "            seq_len = len(tag_list)\n", "            for i in range(seq_len - 1):\n", "                current_tagid = _____\n", "                next_tagid = _____\n", "                self.A[current_tagid][next_tagid] += _____\n", "        # 问题：如果某元素没有出现过，该位置为0，这在后续的计算中是不允许的\n", "        # 解决方法：我们将等于0的概率加上很小的数\n", "        self.A[self.A == 0.] = 1e-10\n", "        # 归一化\n", "        self.A = _____\n", "        #-----------------------------------------------1--------------------------------------------\n", "\n", "        # 估计观测概率矩阵\n", "        #-----------------------------------------------2--------------------------------------------\n", "        # TODO: 估计观测概率矩阵\n", "        # 需要：1. 获取标签和词的ID 2. 更新观测计数 3. 归一化\n", "        for tag_list, word_list in zip(tag_lists, word_lists):\n", "            assert len(tag_list) == len(word_list)\n", "            for tag, word in zip(tag_list, word_list):\n", "                tag_id = _____\n", "                word_id = _____\n", "                self.B[tag_id][word_id] += _____\n", "        self.B[self.B == 0.] = 1e-10\n", "        # 归一化\n", "        self.B = _____\n", "        #-----------------------------------------------2--------------------------------------------\n", "\n", "        # 估计初始状态概率\n", "        #-----------------------------------------------3--------------------------------------------\n", "        # TODO: 估计初始状态概率分布\n", "        # 需要：1. 获取序列第一个标签ID 2. 更新初始状态计数 3. 归一化\n", "        for tag_list in tag_lists:\n", "            init_tagid = _____\n", "            self.Pi[init_tagid] += _____\n", "        self.Pi[self.Pi == 0.] = 1e-10\n", "        # 归一化\n", "        self.Pi = _____\n", "        #-----------------------------------------------3--------------------------------------------\n", "\n", "    def test(self, word_lists, word2id, tag2id):\n", "        pred_tag_lists = []\n", "        for word_list in word_lists:\n", "            pred_tag_list = self.decoding(word_list, word2id, tag2id)\n", "            pred_tag_lists.append(pred_tag_list)\n", "        return pred_tag_lists"]}, {"cell_type": "code", "execution_count": null, "id": "hmm-viterbi", "metadata": {}, "outputs": [], "source": ["    def decoding(self, word_list, word2id, tag2id):\n", "        \"\"\"\n", "        使用维特比算法对给定观测序列求状态序列\n", "        \"\"\"\n", "        # 采用对数概率，避免下溢\n", "        A = torch.log(self.A)\n", "        B = torch.log(self.B)\n", "        Pi = torch.log(self.Pi)\n", "\n", "        # 初始化维比特矩阵\n", "        seq_len = len(word_list)\n", "        viterbi = torch.zeros(self.N, seq_len)\n", "        backpointer = torch.zeros(self.N, seq_len).long()\n", "\n", "        # 第一步\n", "        start_wordid = word2id.get(word_list[0], None)\n", "        Bt = B.t()\n", "        if start_wordid is None:\n", "            bt = torch.log(torch.ones(self.N) / self.N)\n", "        else:\n", "            bt = Bt[start_wordid]\n", "        viterbi[:, 0] = Pi + bt\n", "        backpointer[:, 0] = -1\n", "\n", "        # 递推\n", "        for step in range(1, seq_len):\n", "            wordid = word2id.get(word_list[step], None)\n", "            if wordid is None:\n", "                bt = torch.log(torch.ones(self.N) / self.N)\n", "            else:\n", "                bt = Bt[wordid]\n", "            for tag_id in range(len(tag2id)):\n", "                #-----------------------------------------------4--------------------------------------------\n", "                # TODO: 实现维特比算法递推公式\n", "                # 需要：1. 计算前一步概率+转移概率+观测概率 2. 找到最大值和对应状态\n", "                max_prob, max_id = torch.max(\n", "                    _____\n", "                )                \n", "                viterbi[tag_id, step] = _____\n", "                #-----------------------------------------------4--------------------------------------------\n", "                backpointer[tag_id, step] = max_id\n", "\n", "        # 终止\n", "        #-----------------------------------------------5--------------------------------------------\n", "        # TODO: 找到最优路径的终点状态\n", "        # 需要：从最后一个时间步的所有状态中找到概率最大的状态\n", "        best_path_prob, best_path_pointer = torch.max(\n", "            _____\n", "        )\n", "        #-----------------------------------------------5--------------------------------------------\n", "\n", "        # 回溯，求最优路径\n", "        best_path_pointer = best_path_pointer.item()\n", "        best_path = [best_path_pointer]\n", "        #-----------------------------------------------6--------------------------------------------\n", "        # TODO: 实现路径回溯\n", "        # 需要：从终点开始，利用backpointer逐步回溯到起点\n", "        for back_step in range(seq_len-1, 0, -1):\n", "            best_path_pointer = _____\n", "            best_path_pointer = best_path_pointer.item()\n", "            best_path.append(best_path_pointer)\n", "        #-----------------------------------------------6--------------------------------------------\n", "        \n", "        # 将tag_id组成的序列转化为tag\n", "        assert len(best_path) == len(word_list)\n", "        id2tag = dict((id_, tag) for tag, id_ in tag2id.items())\n", "        tag_list = [id2tag[id_] for id_ in reversed(best_path)]\n", "\n", "        return tag_list\n", "\n", "# 将decoding方法添加到HMM类中\n", "HMM.decoding = decoding"]}, {"cell_type": "markdown", "id": "bilstm-model", "metadata": {}, "source": ["### BiLSTM模型实现\n", "\n", "双向LSTM模型用于序列标注任务。"]}, {"cell_type": "code", "execution_count": null, "id": "bilstm-class", "metadata": {}, "outputs": [], "source": ["class BiLSTM(nn.Module):\n", "    def __init__(self, vocab_size, emb_size, hidden_size, out_size):\n", "        \"\"\"初始化参数：\n", "            vocab_size:字典的大小\n", "            emb_size:词向量的维数\n", "            hidden_size：隐向量的维数\n", "            out_size:标注的种类\n", "        \"\"\"\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        #-----------------------------------------------1--------------------------------------------\n", "        # TODO: 定义词向量层\n", "        # 需要：词典大小、词向量维度\n", "        self.embedding = _____\n", "        #-----------------------------------------------1--------------------------------------------\n", "        #-----------------------------------------------2--------------------------------------------\n", "        # TODO: 定义BiLSTM层\n", "        # 需要：输入维度、隐藏层维度、batch_first=True、bidirectional=True\n", "        self.bilstm = nn.LSTM(\n", "            _____\n", "            )\n", "        #-----------------------------------------------2--------------------------------------------\n", "        #-----------------------------------------------3--------------------------------------------\n", "        # TODO: 定义输出全连接层\n", "        # 需要：输入维度（BiLSTM隐藏层维度*2）、输出维度（标签数量）\n", "        self.lin = nn.Linear(\n", "            _____\n", "        )\n", "        #-----------------------------------------------3--------------------------------------------\n", "\n", "    def forward(self, sents_tensor, lengths):\n", "        #-----------------------------------------------4--------------------------------------------\n", "        # TODO: 实现BiLSTM前向传播\n", "        # 需要：1. 词向量化 2. BiLSTM编码 3. 全连接层输出\n", "        emb = _____\n", "\n", "        packed = pack_padded_sequence(emb, lengths, batch_first=True)\n", "        rnn_out, _ = _____\n", "        rnn_out, _ = pad_packed_sequence(rnn_out, batch_first=True)\n", "\n", "        scores = _____\n", "        #-----------------------------------------------4--------------------------------------------\n", "        return scores\n", "\n", "    def test(self, sents_tensor, lengths, _):\n", "        \"\"\"第三个参数不会用到，加它是为了与BiLSTM_CRF保持同样的接口\"\"\"\n", "        logits = self.forward(sents_tensor, lengths)  # [B, L, out_size]\n", "        _, batch_tagids = torch.max(logits, dim=2)\n", "\n", "        return batch_tagids"]}, {"cell_type": "markdown", "id": "evaluation-metrics", "metadata": {}, "source": ["### 评估指标实现\n", "\n", "计算精确率、召回率和F1分数。"]}, {"cell_type": "code", "execution_count": null, "id": "metrics-class", "metadata": {}, "outputs": [], "source": ["class Metrics(object):\n", "    \"\"\"用于评价模型，计算每个标签的精确率，召回率，F1分数\"\"\"\n", "\n", "    def __init__(self, golden_tags, predict_tags, remove_O=False):\n", "        # [[t1, t2], [t3, t4]...] --> [t1, t2, t3, t4...]\n", "        self.golden_tags = flatten_lists(golden_tags)\n", "        self.predict_tags = flatten_lists(predict_tags)\n", "\n", "        if remove_O:  # 将O标记移除，只关心实体标记\n", "            self._remove_Otags()\n", "\n", "        # 辅助计算的变量\n", "        self.tagset = set(self.golden_tags)\n", "        self.correct_tags_number = self.count_correct_tags()\n", "        self.predict_tags_counter = Counter(self.predict_tags)\n", "        self.golden_tags_counter = Counter(self.golden_tags)\n", "\n", "        # 计算精确率\n", "        self.precision_scores = self.cal_precision()\n", "\n", "        # 计算召回率\n", "        self.recall_scores = self.cal_recall()\n", "\n", "        # 计算F1分数\n", "        self.f1_scores = self.cal_f1()\n", "\n", "    def cal_precision(self):\n", "        \"\"\"计算精确率\"\"\"\n", "        precision_scores = {}\n", "        #------------------------------------------------------1---------------------------------------------\n", "        # TODO: 计算精确率\n", "        # 需要：正确预测的标签数量 / 预测的标签总数量\n", "        for tag in self.tagset:\n", "            # 检查预测标签数量是否为0，避免除以零\n", "            if self.predict_tags_counter[tag] == 0:\n", "                precision_scores[tag] = 0.0\n", "            else:\n", "                precision_scores[tag] = _____\n", "        #------------------------------------------------------1---------------------------------------------\n", "        return precision_scores\n", "\n", "    def cal_recall(self):\n", "        \"\"\"计算召回率\"\"\"\n", "        recall_scores = {}\n", "        #------------------------------------------------------2---------------------------------------------\n", "        # TODO: 计算召回率\n", "        # 需要：正确预测的标签数量 / 真实标签总数量\n", "        for tag in self.tagset:\n", "            # 检查真实标签数量是否为0，避免除以零\n", "            if self.golden_tags_counter[tag] == 0:\n", "                recall_scores[tag] = 0.0\n", "            else:\n", "                recall_scores[tag] = _____\n", "        #------------------------------------------------------2---------------------------------------------            \n", "        return recall_scores\n", "\n", "    def cal_f1(self):\n", "        \"\"\"计算F1分数\"\"\"\n", "        f1_scores = {}\n", "        #------------------------------------------------------3---------------------------------------------\n", "        # TODO: 计算F1分数\n", "        # 需要：2 * 精确率 * 召回率 / (精确率 + 召回率)\n", "        for tag in self.tagset:\n", "            p, r = self.precision_scores[tag], self.recall_scores[tag]\n", "            # 只有当精确率和召回率都为0时，F1才为0\n", "            if p == 0 and r == 0:\n", "                f1_scores[tag] = 0.0\n", "            else:\n", "                f1_scores[tag] = _____\n", "        #------------------------------------------------------3---------------------------------------------\n", "        return f1_scores\n", "    \n", "    def count_correct_tags(self):\n", "        \"\"\"计算正确预测的标签数量\"\"\"\n", "        correct_dict = {}\n", "        for gold_tag, pred_tag in zip(self.golden_tags, self.predict_tags):\n", "            if gold_tag == pred_tag:\n", "                if gold_tag not in correct_dict:\n", "                    correct_dict[gold_tag] = 0\n", "                correct_dict[gold_tag] += 1\n", "        return correct_dict\n", "    \n", "    def _remove_O<PERSON>s(self):\n", "        \"\"\"移除O标签\"\"\"\n", "        length = len(self.golden_tags)\n", "        O_tag_indices = [i for i in range(length) if self.golden_tags[i] == 'O']\n", "        \n", "        self.golden_tags = [tag for i, tag in enumerate(self.golden_tags) if i not in O_tag_indices]\n", "        self.predict_tags = [tag for i, tag in enumerate(self.predict_tags) if i not in O_tag_indices]\n", "        print(\"移除了{}个O标签\".format(len(O_tag_indices)))\n", "    \n", "    def report_scores(self):\n", "        \"\"\"打印评估结果\"\"\"\n", "        print(\"\\n各标签的评估结果:\")\n", "        print(\"{:>10s}\\t{:>10s}\\t{:>10s}\\t{:>10s}\".format(\"标签\", \"精确率\", \"召回率\", \"F1分数\"))\n", "        for tag in self.tagset:\n", "            print(\"{:>10s}\\t{:>10.4f}\\t{:>10.4f}\\t{:>10.4f}\".format(\n", "                tag, self.precision_scores[tag], self.recall_scores[tag], self.f1_scores[tag]\n", "            ))"]}, {"cell_type": "markdown", "id": "task2-header", "metadata": {}, "source": ["## 任务二：三元组提取（KGC）\n", "\n", "### 概述\n", "知识图谱构建（KGC）任务旨在从文本中提取实体关系三元组，构建结构化的知识图谱。"]}, {"cell_type": "markdown", "id": "kgc-data-processing", "metadata": {}, "source": ["## 1. 数据处理（data/raw_data_process.py）\n", "\n", "将JSONL格式的数据转换为文本和三元组文件。"]}, {"cell_type": "code", "execution_count": null, "id": "jsonl-to-txt", "metadata": {}, "outputs": [], "source": ["def jsonl_to_txt(jsonl_file_path, text_output_path, triple_output_path):\n", "    \"\"\"\n", "    将JSONL文件转换为两个TXT文件：\n", "    - text_output_path: 每行存储\"text\"字段内容\n", "    - triple_output_path: 每行存储\"triple_list\"的JSON字符串\n", "    \"\"\"\n", "    try:\n", "        with open(jsonl_file_path, 'r', encoding='utf-8') as jsonl_file, \\\n", "                open(text_output_path, 'w', encoding='utf-8') as text_file, \\\n", "                open(triple_output_path, 'w', encoding='utf-8') as triple_file:\n", "\n", "            line_count = 0\n", "            for line in jsonl_file:\n", "                #------------------------------------------------------1---------------------------------------------\n", "                # TODO: 解析JSONL文件并提取字段\n", "                # 需要：1. 解析JSON数据 2. 提取text字段 3. 提取triple_list字段\n", "                # 解析JSONL每行数据\n", "                data = json._____\n", "\n", "                # 提取text字段并写入文本文件\n", "                text_content = data._____\n", "                text_file.write(text_content + \"\\n\")\n", "\n", "                # 提取triple_list字段并序列化为JSON字符串\n", "                triple_list = data._____\n", "                triple_file.write(json.dumps(triple_list, ensure_ascii=False) + \"\\n\")\n", "                #------------------------------------------------------1---------------------------------------------\n", "                line_count += 1\n", "                if line_count % 1000 == 0:  # 每处理1000行打印进度\n", "                    print(f\"已处理 {line_count} 行...\")\n", "\n", "            print(f\"转换完成！共处理 {line_count} 行数据\")\n", "            print(f\"文本文件保存至: {os.path.abspath(text_output_path)}\")\n", "            print(f\"三元组文件保存至: {os.path.abspath(triple_output_path)}\")\n", "\n", "    except FileNotFoundError:\n", "        print(f\"错误: 文件 {jsonl_file_path} 不存在\")\n", "    except json.JSONDecodeError as e:\n", "        print(f\"JSON解析错误: {e}\")\n", "    except Exception as e:\n", "        print(f\"未知错误: {e}\")\n", "\n", "# 示例用法\n", "# jsonl_to_txt(\n", "#     jsonl_file_path=\"CMeIE-V2_test_triples.jsonl\",\n", "#     text_output_path=\"source.txt\",\n", "#     triple_output_path=\"target.txt\"\n", "# )"]}, {"cell_type": "markdown", "id": "llm-integration", "metadata": {}, "source": ["## 2. 大模型集成（需要外部文件支持）\n", "\n", "由于大模型相关代码涉及复杂的模型加载和推理，建议使用外部文件 `run_llm.py` 来运行。\n", "\n", "### 使用说明：\n", "\n", "1. **配置 run_llm.py 文件**：\n", "   - 设置 `text_path`（源文件路径）\n", "   - 设置 `result_path`（保存文件路径）\n", "   - 配置 `few_shot` 示例\n", "   - 设计 `prompt` 提示词\n", "\n", "2. **填空内容**："]}, {"cell_type": "code", "execution_count": null, "id": "run-llm-config", "metadata": {}, "outputs": [], "source": ["# 在 run_llm.py 文件中需要填写的内容：\n", "\n", "# TODO: 添加text_path（源文件路径）、result_path（保存文件路径）参数\n", "text_path = \"data/source.txt\"  # 示例路径\n", "result_path = \"data/result.txt\"  # 示例路径\n", "\n", "# TODO: 设置few_shot示例\n", "few_shot = {\n", "    \"user input text\": \"张三是北京大学的教授，专门研究人工智能。\",\n", "    \"output triples\": [[\"张三\", \"职业\", \"教授\"], [\"张三\", \"工作单位\", \"北京大学\"], [\"张三\", \"研究领域\", \"人工智能\"]]\n", "}\n", "\n", "# TODO: 设置prompt提示词\n", "prompt = (\n", "    \"你是一个专业的信息抽取专家。请从给定的文本中提取实体关系三元组。\"\n", "    \"三元组的格式为[实体1, 关系, 实体2]。请确保提取的关系准确且有意义。\"\n", "    \"只返回三元组列表，不要包含其他内容。\"\n", ")\n", "\n", "print(\"配置示例已显示，请在 run_llm.py 文件中填写相应内容\")"]}, {"cell_type": "markdown", "id": "knowledge-graph", "metadata": {}, "source": ["## 3. 知识图谱构建与推理\n", "\n", "基于提取的三元组构建知识图谱并进行推理。"]}, {"cell_type": "code", "execution_count": null, "id": "kg-class", "metadata": {}, "outputs": [], "source": ["if NETWORKX_AVAILABLE:\n", "    class KnowledgeGraph:\n", "        \"\"\"知识图谱构建和分析类\"\"\"\n", "        \n", "        def __init__(self):\n", "            self.graph = nx.DiGraph()  # 有向图\n", "            self.entity_types = defaultdict(set)  # 实体类型映射\n", "            self.relation_stats = Counter()  # 关系统计\n", "            \n", "        def build_from_triples(self, triples_file_path):\n", "            \"\"\"\n", "            从三元组文件构建知识图谱\n", "            \"\"\"\n", "            try:\n", "                with open(triples_file_path, 'r', encoding='utf-8') as f:\n", "                    for line_num, line in enumerate(f, 1):\n", "                        line = line.strip()\n", "                        if not line:\n", "                            continue\n", "                        \n", "                        try:\n", "                            #------------------------------------------------------1---------------------------------------------\n", "                            # TODO: 解析三元组数据并构建知识图谱\n", "                            # 需要：1. 解析JSON/Python格式数据 2. 添加节点和边到图中 3. 统计关系类型\n", "                            try:\n", "                                triples = json.loads(line)  # 解析标准JSON（双引号）\n", "                            except json.JSONDecodeError:\n", "                                triples = ast.literal_eval(line)  # 解析Python格式（单引号）\n", "\n", "                            for triple in triples:\n", "                                if len(triple) >= 3:\n", "                                    head, relation, tail = triple[0], triple[1], triple[2]\n", "\n", "                                    # 添加实体节点和关系边到知识图谱\n", "                                    self.graph._____\n", "                                    self.graph._____\n", "                                    self.graph._____\n", "\n", "                                    # 统计关系类型\n", "                                    self.relation_stats[relation] += 1\n", "                            #------------------------------------------------------1---------------------------------------------\n", "\n", "                        except (j<PERSON>.<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SyntaxError):\n", "                            print(f\"第{line_num}行JSON解析错误，跳过\")\n", "                            continue\n", "                            \n", "            except FileNotFoundError:\n", "                print(f\"文件 {triples_file_path} 不存在\")\n", "                return\n", "            \n", "            print(f\"知识图谱构建完成！\")\n", "            print(f\"节点数量: {self.graph.number_of_nodes()}\")\n", "            print(f\"边数量: {self.graph.number_of_edges()}\")\n", "            print(f\"关系类型数量: {len(self.relation_stats)}\")\n", "        \n", "        def get_neighbors_by_relation(self, entity, relation_type):\n", "            \"\"\"\n", "            根据关系类型获取实体的邻居\n", "            \"\"\"\n", "            neighbors = []\n", "            #------------------------------------------------------2---------------------------------------------\n", "            # TODO: 实现基于关系类型的实体邻居查找\n", "            # 需要：遍历实体的所有邻居，筛选出指定关系类型的邻居\n", "            if entity in self.graph:\n", "                for neighbor in self.graph.neighbors(entity):\n", "                    edge_data = self.graph.get_edge_data(entity, neighbor)\n", "                    if edge_data and edge_data.get('relation') == relation_type:\n", "                        neighbors._____\n", "            #------------------------------------------------------2---------------------------------------------\n", "            return neighbors\n", "        \n", "        def find_shortest_path(self, start_entity, end_entity):\n", "            \"\"\"\n", "            查找两个实体之间的最短路径\n", "            \"\"\"\n", "            try:\n", "                #------------------------------------------------------3---------------------------------------------\n", "                # TODO: 实现知识图谱中的最短路径查找\n", "                # 需要：使用networkx的最短路径算法\n", "                path = nx._____\n", "                #------------------------------------------------------3---------------------------------------------\n", "                return path\n", "            except nx.NetworkXNoPath:\n", "                return None\n", "            except nx.NodeNotFound:\n", "                return None\n", "else:\n", "    print(\"NetworkX未安装，知识图谱功能不可用\")\n", "    class KnowledgeGraph:\n", "        def __init__(self):\n", "            print(\"请安装networkx库以使用知识图谱功能\")\n", "        \n", "        def build_from_triples(self, triples_file_path):\n", "            print(\"请安装networkx库以使用知识图谱功能\")"]}, {"cell_type": "markdown", "id": "kg-reasoning", "metadata": {}, "source": ["## 4. 知识图谱推理\n", "\n", "基于构建的知识图谱进行推理分析。"]}, {"cell_type": "code", "execution_count": null, "id": "kg-reasoning-class", "metadata": {}, "outputs": [], "source": ["class KGReasoning:\n", "    \"\"\"知识图谱推理类\"\"\"\n", "    \n", "    def __init__(self, knowledge_graph):\n", "        self.kg = knowledge_graph\n", "    \n", "    def find_disease_symptoms(self, disease_name):\n", "        \"\"\"\n", "        查找疾病的症状\n", "        \"\"\"\n", "        #------------------------------------------------------1---------------------------------------------\n", "        # TODO: 实现基于知识图谱的疾病症状查找\n", "        # 需要：调用知识图谱的邻居查找方法，查找\"症状\"关系\n", "        symptoms = self.kg._____\n", "        #------------------------------------------------------1---------------------------------------------\n", "        return symptoms\n", "    \n", "    def calculate_jaccard_similarity(self, set1, set2):\n", "        \"\"\"\n", "        计算两个集合的Jaccard相似度\n", "        \"\"\"\n", "        #------------------------------------------------------2---------------------------------------------\n", "        # TODO: 实现Jaccard相似度计算\n", "        # 需要：计算交集大小 / 并集大小\n", "        if not set1 and not set2:\n", "            return 0.0\n", "        \n", "        intersection = len(_____)\n", "        union = len(_____)\n", "        \n", "        if union == 0:\n", "            return 0.0\n", "        \n", "        return _____\n", "        #------------------------------------------------------2---------------------------------------------\n", "    \n", "    def find_similar_diseases(self, target_symptoms, top_k=5):\n", "        \"\"\"\n", "        基于症状相似性查找相似疾病\n", "        \"\"\"\n", "        target_symptoms_set = set(target_symptoms)\n", "        disease_similarities = []\n", "        \n", "        # 获取所有疾病实体\n", "        if hasattr(self.kg, 'graph'):\n", "            all_entities = list(self.kg.graph.nodes())\n", "        else:\n", "            print(\"知识图谱未正确初始化\")\n", "            return []\n", "        \n", "        for entity in all_entities:\n", "            #------------------------------------------------------3---------------------------------------------\n", "            # TODO: 实现基于症状相似性的疾病推理\n", "            # 需要：1. 获取疾病症状 2. 计算相似度 3. 排序返回结果\n", "            entity_symptoms = _____\n", "            \n", "            if entity_symptoms:  # 只考虑有症状的实体\n", "                entity_symptoms_set = set(entity_symptoms)\n", "                similarity = _____\n", "                \n", "                if similarity > 0:\n", "                    disease_similarities.append((entity, similarity, entity_symptoms))\n", "            #------------------------------------------------------3---------------------------------------------\n", "        \n", "        # 按相似度排序并返回前top_k个\n", "        disease_similarities.sort(key=lambda x: x[1], reverse=True)\n", "        return disease_similarities[:top_k]"]}, {"cell_type": "markdown", "id": "evaluation-kgc", "metadata": {}, "source": ["## 5. 三元组提取评估\n", "\n", "评估三元组提取的准确性。"]}, {"cell_type": "code", "execution_count": null, "id": "triple-evaluation", "metadata": {}, "outputs": [], "source": ["def evaluate_triple_extraction(predicted_file, ground_truth_file):\n", "    \"\"\"\n", "    评估三元组提取结果\n", "    \"\"\"\n", "    def load_triples(file_path):\n", "        \"\"\"加载三元组数据\"\"\"\n", "        triples_list = []\n", "        try:\n", "            with open(file_path, 'r', encoding='utf-8') as f:\n", "                for line in f:\n", "                    line = line.strip()\n", "                    if line:\n", "                        try:\n", "                            triples = json.loads(line)\n", "                        except json.JSONDecodeError:\n", "                            triples = ast.literal_eval(line)\n", "                        \n", "                        # 将三元组转换为元组集合，便于比较\n", "                        triple_set = set()\n", "                        for triple in triples:\n", "                            if len(triple) >= 3:\n", "                                triple_set.add((triple[0], triple[1], triple[2]))\n", "                        triples_list.append(triple_set)\n", "        except FileNotFoundError:\n", "            print(f\"文件 {file_path} 不存在\")\n", "            return []\n", "        \n", "        return triples_list\n", "    \n", "    # 加载预测和真实数据\n", "    predicted_triples = load_triples(predicted_file)\n", "    ground_truth_triples = load_triples(ground_truth_file)\n", "    \n", "    if len(predicted_triples) != len(ground_truth_triples):\n", "        print(f\"警告：预测数据({len(predicted_triples)})和真实数据({len(ground_truth_triples)})长度不一致\")\n", "        min_len = min(len(predicted_triples), len(ground_truth_triples))\n", "        predicted_triples = predicted_triples[:min_len]\n", "        ground_truth_triples = ground_truth_triples[:min_len]\n", "    \n", "    # 计算评估指标\n", "    total_tp = 0\n", "    total_fp = 0\n", "    total_fn = 0\n", "    \n", "    for pred_set, true_set in zip(predicted_triples, ground_truth_triples):\n", "        #------------------------------------------------------1---------------------------------------------\n", "        # TODO: 计算TP、FP、FN\n", "        # 需要：1. 计算交集（TP） 2. 计算预测但不在真实中的（FP） 3. 计算真实但未预测的（FN）\n", "        tp = len(_____)\n", "        fp = len(pred_set - true_set)\n", "        fn = len(true_set - pred_set)\n", "        #------------------------------------------------------1---------------------------------------------\n", "        \n", "        total_tp += tp\n", "        total_fp += fp\n", "        total_fn += fn\n", "    \n", "    # 计算精确率、召回率和F1分数\n", "    precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0\n", "    recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0\n", "    \n", "    #------------------------------------------------------2---------------------------------------------\n", "    # TODO: 计算F1分数并处理边界情况\n", "    # 需要：2 * precision * recall / (precision + recall)\n", "    if precision + recall > 0:\n", "        f1 = _____\n", "    else:\n", "        f1 = 0\n", "    #------------------------------------------------------2---------------------------------------------\n", "    \n", "    print(f\"\\n三元组提取评估结果:\")\n", "    print(f\"精确率 (Precision): {precision:.4f}\")\n", "    print(f\"召回率 (<PERSON>call): {recall:.4f}\")\n", "    print(f\"F1分数: {f1:.4f}\")\n", "    print(f\"总TP: {total_tp}, 总FP: {total_fp}, 总FN: {total_fn}\")\n", "    \n", "    return precision, recall, f1"]}, {"cell_type": "markdown", "id": "usage-examples", "metadata": {}, "source": ["## 使用示例\n", "\n", "以下是各个模块的使用示例。"]}, {"cell_type": "markdown", "id": "ner-example", "metadata": {}, "source": ["### NER任务使用示例"]}, {"cell_type": "code", "execution_count": null, "id": "ner-usage", "metadata": {}, "outputs": [], "source": ["# NER任务使用示例\n", "def run_ner_example():\n", "    print(\"=== NER任务示例 ===\")\n", "    \n", "    # 1. 数据处理示例\n", "    print(\"\\n1. 数据处理示例\")\n", "    sample_text = \"张三是北京大学的教授。李四在清华大学工作。\"\n", "    sentences = split_sentences(sample_text)\n", "    print(f\"原文本: {sample_text}\")\n", "    print(f\"分割结果: {sentences}\")\n", "    \n", "    # 2. HMM模型示例（需要训练数据）\n", "    print(\"\\n2. HMM模型示例\")\n", "    print(\"HMM模型需要训练数据，请确保有BIOES格式的训练文件\")\n", "    \n", "    # 3. 评估示例\n", "    print(\"\\n3. 评估示例\")\n", "    # 模拟一些预测和真实标签\n", "    golden_tags = [['B-PER', 'I-PER', 'O', 'B-ORG', 'I-ORG']]\n", "    predict_tags = [['B-<PERSON><PERSON>', 'I-PER', 'O', 'B-ORG', 'O']]\n", "    \n", "    try:\n", "        metrics = Metrics(golden_tags, predict_tags)\n", "        metrics.report_scores()\n", "    except Exception as e:\n", "        print(f\"评估示例运行出错: {e}\")\n", "\n", "# 运行NER示例\n", "# run_ner_example()"]}, {"cell_type": "markdown", "id": "kgc-example", "metadata": {}, "source": ["### KGC任务使用示例"]}, {"cell_type": "code", "execution_count": null, "id": "kgc-usage", "metadata": {}, "outputs": [], "source": ["# KGC任务使用示例\n", "def run_kgc_example():\n", "    print(\"=== KGC任务示例 ===\")\n", "    \n", "    # 1. 数据处理示例\n", "    print(\"\\n1. 数据处理示例\")\n", "    # 创建示例JSONL数据\n", "    sample_data = {\n", "        \"text\": \"张三是北京大学的教授，专门研究人工智能。\",\n", "        \"triple_list\": [[\"张三\", \"职业\", \"教授\"], [\"张三\", \"工作单位\", \"北京大学\"], [\"张三\", \"研究领域\", \"人工智能\"]]\n", "    }\n", "    print(f\"示例数据: {sample_data}\")\n", "    \n", "    # 2. 知识图谱构建示例\n", "    print(\"\\n2. 知识图谱构建示例\")\n", "    if NETWORKX_AVAILABLE:\n", "        kg = KnowledgeGraph()\n", "        \n", "        # 手动添加一些三元组\n", "        sample_triples = [\n", "            [\"张三\", \"职业\", \"教授\"],\n", "            [\"张三\", \"工作单位\", \"北京大学\"],\n", "            [\"李四\", \"职业\", \"学生\"],\n", "            [\"李四\", \"就读学校\", \"北京大学\"]\n", "        ]\n", "        \n", "        for triple in sample_triples:\n", "            head, relation, tail = triple\n", "            kg.graph.add_node(head, type='entity')\n", "            kg.graph.add_node(tail, type='entity')\n", "            kg.graph.add_edge(head, tail, relation=relation)\n", "        \n", "        print(f\"知识图谱节点数: {kg.graph.number_of_nodes()}\")\n", "        print(f\"知识图谱边数: {kg.graph.number_of_edges()}\")\n", "        \n", "        # 3. 推理示例\n", "        print(\"\\n3. 推理示例\")\n", "        reasoning = KGReasoning(kg)\n", "        \n", "        # 查找与北京大学相关的实体\n", "        related_entities = kg.get_neighbors_by_relation(\"北京大学\", \"工作单位\")\n", "        print(f\"在北京大学工作的人: {related_entities}\")\n", "        \n", "    else:\n", "        print(\"NetworkX未安装，无法运行知识图谱示例\")\n", "    \n", "    # 4. 评估示例\n", "    print(\"\\n4. 评估示例\")\n", "    print(\"三元组提取评估需要预测文件和真实文件，请参考evaluate_triple_extraction函数\")\n", "\n", "# 运行KGC示例\n", "# run_kgc_example()"]}, {"cell_type": "markdown", "id": "instructions", "metadata": {}, "source": ["## 填空指导说明\n", "\n", "### 任务一（NER）填空提示：\n", "\n", "1. **数据处理部分**：\n", "   - 步骤1：`sentences.append(text[start:i+1])`, `i + 1`, `sentences.append(text[start:])`\n", "   - 步骤2：`f'S-{entity_type}'`, `f'B-{entity_type}'`, `f'I-{entity_type}'`, `f'E-{entity_type}'`\n", "   - 步骤3：`process_entity(sentence, sent_text, entity, tags)`\n", "   - 步骤4：`json.load(f)`, `convert_to_bioes(json_data)`, `all_bioes_lines.extend(bioes_lines)`\n", "\n", "2. **模型实现部分**：\n", "   - 数据读取：`line.strip().split()`, `word_lists.append(word_list)`, `tag_lists.append(tag_list)`\n", "   - 词汇映射：`build_map(word_lists)`, `build_map(tag_lists)`\n", "   - HMM训练：`tag2id[tag_list[i]]`, `tag2id[tag_list[i+1]]`, `1`, `self.A / self.A.sum(dim=1, keepdim=True)`\n", "   - BiLSTM：`nn.Embedding(vocab_size, emb_size)`, `emb_size, hidden_size, batch_first=True, bidirectional=True`\n", "\n", "3. **评估部分**：\n", "   - 精确率：`self.correct_tags_number.get(tag, 0) / self.predict_tags_counter[tag]`\n", "   - 召回率：`self.correct_tags_number.get(tag, 0) / self.golden_tags_counter[tag]`\n", "   - F1分数：`2 * p * r / (p + r)`\n", "\n", "### 任务二（KGC）填空提示：\n", "\n", "1. **数据处理**：\n", "   - `json.loads(line)`, `data.get('text', '')`, `data.get('triple_list', [])`\n", "\n", "2. **知识图谱构建**：\n", "   - `self.graph.add_node(head, type='entity')`, `self.graph.add_node(tail, type='entity')`, `self.graph.add_edge(head, tail, relation=relation)`\n", "   - `neighbors.append(neighbor)`\n", "   - `nx.shortest_path(self.graph, start_entity, end_entity)`\n", "\n", "3. **推理和评估**：\n", "   - `self.kg.get_neighbors_by_relation(disease_name, '症状')`\n", "   - `set1 & set2`, `set1 | set2`, `intersection / union`\n", "   - `pred_set & true_set`, `2 * precision * recall / (precision + recall)`"]}, {"cell_type": "markdown", "id": "conclusion", "metadata": {}, "source": ["## 总结\n", "\n", "本文件整合了自然语言处理模块A的两个主要任务：\n", "\n", "### 任务一：命名实体提取（NER）\n", "- ✅ **数据处理**：完整实现了BIOES格式转换\n", "- ✅ **模型实现**：包含HMM、BiLSTM等多种模型的框架\n", "- ✅ **评估指标**：实现了精确率、召回率、F1分数计算\n", "- 🔧 **填空内容**：主要集中在模型训练和推理的核心算法部分\n", "\n", "### 任务二：三元组提取（KGC）\n", "- ✅ **数据处理**：实现了JSONL到文本的转换\n", "- ⚠️ **大模型集成**：由于复杂性，建议使用外部文件`run_llm.py`\n", "- ✅ **知识图谱构建**：基于NetworkX实现图结构构建\n", "- ✅ **推理分析**：实现了基于图的推理算法\n", "- ✅ **评估指标**：实现了三元组提取的评估方法\n", "\n", "### 使用建议：\n", "1. **环境准备**：确保安装必要的依赖库（torch, sklearn_crfsuite, networkx, modelscope等）\n", "2. **数据准备**：准备相应格式的训练和测试数据\n", "3. **模型训练**：根据填空提示完成模型实现\n", "4. **外部文件**：对于大模型相关功能，使用`run_llm.py`文件\n", "5. **逐步测试**：建议先运行简单示例，再处理完整数据集\n", "\n", "### 注意事项：\n", "- 部分功能依赖外部库，请根据实际环境调整\n", "- 大模型功能需要足够的计算资源\n", "- 建议在填空完成后运行相应的测试用例验证正确性"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}