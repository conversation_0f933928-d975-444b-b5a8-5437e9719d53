模块 C、综合工程技术应用
一、任务要求 
根据项目要求完成人工智能综合工程技术应用代码开发，将“resource/task”目录中每个步骤的输出结果和填写的代码截图并粘贴在“竞赛任务应答书.doc”的指定位置中，其余过程文件按要求放置于work文件夹的指定文职。最后按照《选手指引》要求将“竞赛任务应答书.doc”存放于work文件夹，并导出压缩包，保存至指定位置。  
二、任务环境 
硬件资源:高性能GPU计算机、边缘设备场景验证平台、人工智能模型训练系统 
软件资源:“竞赛资源.zip”、pytorch深度学习框架、模型量化软件、模型推理验证平台
三、任务说明 
1、药品知识问答 
在现实医疗场景中，药品的种类繁多且不断更新，其功效、用法用量、禁忌、不良反应等信息复杂多样且相互关联。为满足医疗从业者、患者及普通民众对药品知识的精准获取需求，同时确保在面对不断涌现的新药及药品相关复杂问题时，问答系统仍能保持高效准确，我们设置一项药品知识问答任务。通过对优秀的通用基座模型进行大量药品图文对的训练，以显著提高通用模型在医疗场景下的泛化能力，适应复杂、开放的药品知识问答需求，对医疗实践、患者自我健康管理以及公众健康知识普及具有重要意义。
同时，随着人工智能技术的飞速发展，边缘计算在各个领域的应用日益广泛。在医疗健康场景中，将训练好的模型部署到边缘设备，实现对药品信息的快速识别与处理，具有极高的实用价值。本任务旨在让考生亲身体验将训练好的模型部署到实际边缘设备的全流程，将理论知识与实践操作紧密结合，提升在真实场景下解决实际问题的能力。考生需将训练好的药品问答模型进行转换、编译，并成功部署到指定的边缘设备中。随后，利用边缘设备的摄像头在真实生活环境中进行拍照，获取药盒正面图片，并通过提供的测试平台调用边缘设备进行问答测试，验证模型在实际应用中的性能和准确性。
进入人工智能模型训练系统，打开“task”文件夹，文件夹中提供了所需的全部任务文件。请打开“task/medicines_qa.ipynb”文件（选择kernel环境应该为module_C_1_env），根据提示在该文件内或其他指定位置编写代码，完成以下操作: 
（1）步骤1 加载模型制作训练集
部分代码已给出，请根据提示，将代码补充完整。 
①请在<1>处补充resize函数代码， 将药品图片resize到合适的大小，并保存到合适路径；
②请在<2>处补充代码，找出药品的正面图，并调用resize函数进行处理。
③请在<3>处补充代码，构造合适的prompt，正确的messages结构，设定适宜的 max_new_tokens，最终提取模型输出。
④请在<4>处补充代码，构造训练数据集并进行保存。
完成后，请顺序运行medicines_qa.ipynb对应的前三个单元格。
⑤请根据<5>提示，构造正确的数据集描述写入“source/LLaMA_Factory/data /dataset_info.json”，并将生成的数据放入“source/LLaMA_Factory/data”文件夹中。
将该步骤单元格运行结果保存在“竞赛任务应答书.doc”中指定位置，将该步骤①至⑤处补充的代码截图和数据构造部分都保存在“竞赛任务应答书.doc”中指定位置。第⑤步，将数据文件放入正确的文件夹后，需要截图vscode左侧展示的source/LLaMA_Factory/data的文件夹目录保存在“竞赛任务应答书.doc”中指定位置（需包含train_data.json）。
（2）步骤2 模型训练
①部分代码已给出，请根据提示，将代码补充完整。 
请根据<1>提示，补充“resource/LLaMA-Factory/example/train_lora/qwen2vl_lora_sft.yaml”训练脚本中相应参数。
②请根据<2>提示命令，开始进行模型训练。
将该步骤①补充的代码截图并保存在“竞赛任务应答书.doc”中指定位置。将②的模型训练日志最后部分截图，保存在“竞赛任务应答书.doc”中指定位置，并将训练的模型所在文件夹“/saves/sft_model”拷贝一份到work文件夹的“models”目录下。
（3）步骤3 模型导出
①请根据<1>提示，补充“resource/LLaMA-Factory/example/merge_lora/qwen2vl_lora_sft.yaml”中相应参数。
②请根据<2>提示命令，开始进行模型导出。
将<2>处的模型导出日志最后部分截图，保存在“竞赛任务应答书.doc”中指定位置，并将训练的模型所在文件夹“/ans/qwen2vl_lora_sft”拷贝一份到work文件夹的“models”目录下。
（4）步骤4 模型能力评测
①请点击平台的远程桌面，选择“多模态模型评测工具”并打开，选择模型为“Qwen2_VL”，并将目录选择对应的“/ans/qwen2vl_lora_sft”保存目录，点击进行模型评测。
将系统模型评测结果进行截图并保存在“竞赛任务应答书.doc”中指定位置，截图需要包含选择模型文件界面及accuracy的输出界面。
（5）步骤5 模型量化与验证
①请将“/ans/qwen2vl_lora_sft”导出的模型文件整个打包，导出到竞赛系统外并解压，选择主目录下的autobit_qt文件夹并在文件夹目录下打开终端，运行命令“sudo ./run_autobit.sh”打开模型转换工具，选择正确页面执行模型量化操作，配置文件使用默认的“W4A16”，量化完成后，将量化完成的截图、转换后的模型所在文件夹的截图均保存在“竞赛任务应答书.doc”中指定位置。
②请阅读主目录下的autobit_qt文件夹下的infer_autoawq_cli.py文件，并根据命令进行一次量化模型的推理验证（注：在终端运行时，还需要添加sudo命令），验证使用文件夹中的demo.jpg，问题可自主编辑。将推理验证的输出截图保存在“竞赛任务应答书.doc”中指定位置。
（6）步骤6.模型转换、编译与部署
①由于模型可能需要每批次输入一定数量的图像，N 可能不满足 temporal_patch_size 的要求。所以我们需要在 N == 1 或 N % temporal_patch_size != 0 的情况下通过重复图像来填充批次。请在<1>处补充代码，使用repeat函数调整图像批次的大小，确保其符合temporal_patch_size。
②请在<2>处补充代码，确保在第一步时，调用视觉模块的前向传播方法,相比于第二步，这里还需要传入包含grid_t, grid_h, grid_w的grid_thw参数（该参数参与模型的位置嵌入计算）。需要确保grid_thw的形状为 (1, 3)，即在第一维增加一个批次维度。
③在<3>处，我们需要调用torch.onnx.export来导出ONNX模型。导出时，需要提供正确的输入张量，设置opset_version参数为19。
④请补全source/make_input_embeds.py中<4>处的代码，这里需要从data/inputs_embeds/加载各inputs_embeds。并在终端运行该文件（需要环境为module_C_2_env，可使用conda activate module_C_2_env命令切换为指定环境）。
⑤请补全source/export_rkllm.py中<5>处的代码，需要设置量化类型为w8a8。并在终端运行该文件（需要环境为module_C_2_env，可使用conda activate module_C_2_env命令切换为指定环境）。
将该步骤①-⑤补充的代码保存在“竞赛任务应答书.doc”中指定位置。将步骤涉及到的运行输出截图均保存在“竞赛任务应答书.doc”中指定位置。
（7）步骤7.板端测试
请按《人工智能工程技术设备使用说明》将rknn模型和rkllm模型传入边缘设备的userdata目录下，编辑边缘设备/etc/inference-test-app目录下的配置文件，确保两个模型的路径正确。
	然后，在边缘设备上打开验证平台，根据平台要求进行测试。
将平台打开的初始界面，以及5个测试结果截图并保存在“竞赛任务应答书.doc”中指定位置。
注:编写完成之后，需要将“竞赛任务应答书.doc”保存至“work”文件夹，然后将“medicines_qa.ipynb”、“make_input_embeds.py”、“export_rkllm.py”文件保存至“work”文件夹的“code”目录，“train_data.json”保存至“work”文件夹的“data”目录。 







