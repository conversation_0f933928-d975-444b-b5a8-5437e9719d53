各难度题目分数比重
为了保证赛项顺利进行，设置70%的基础题目考察选手的工程技术水平，设置30%较难的内容用于区分选手能力。
基础内容（70%）
每个步骤作为一个大得分点，分值区间在2-5分；
2分题目通常为数据预处理步骤，包括数据预处理和简单的数据增强内容；3分题目通常为模型训练步骤，包括损失函数计算、参数更新等内容；4分题目通常为模型结果预测或简单可视化；5分题目通常为模型构造或包含较难功能的步骤。
每个步骤中需实现多个功能，每个功能的作为得分细项，分值区间在0.25-2分。
0.25分题目通常作为一个功能中较为简单的最小部分，如基础“数据预处理” 中的“重置图像大小”等；0.5分题目通常为实现一个简单功能，或，如数据预处理” 中的“获取目标框标签”等；1分题目通常为实现一个正常难度功能，如“冻结模型参数更新”等；2分题目通常为实现较难或流程较为复杂的功能，如完整实现forward函数“计算每个token的logits并返回”等。

各难度题目分数比重
为了保证赛项顺利进行，设置70%的基础题目考察选手的工程技术水平，设置30%较难的内容用于区分选手能力。
能力区分内容（30%）
作为附加步骤穿插在主要流程中，分值区间在2分；
主要内容为功能函数构造，如构造损失函数。
作为附加功能穿插在步骤中，分值区间在1-2分。
1分功能主要为给出提示且实现复杂度一般的完整功能，如“正确构造新的零样本分类器”；2分功能主要为给出提示较少且实现复杂度较高的完整功能，如“使用t-sne可视化实验结果”。

各难度题目分数比重
为了保证题目的难度平衡，对数据处理、模型构造、模型训练和模型测试及分析的题目数量、分数占比进行了分配（统计结果）。
数据预处理和增强（20%左右）
模型构造和优化方法选择（30%左右）
模型训练（10%左右）
模型测试及分析（30%左右）