            项目名称								
            人工智能工程技术职业技能项目								
            标准	分数							
        A	自然语言处理技术应用	40.00							
        B	计算机视觉技术应用	35.00							
        C	综合工程技术应用	25.00							
                                            
                                            
                                            
Sub Criteria ID	Sub Criteria Name or Description	Aspect Type     M = Meas  J = Judg	Aspect - Description	Judg Score	Extra Aspect Description (Meas )                                    OR                                                                                                                     Judgement Score Description (Judg only)	Requirement      or Nominal           Size (Meas Only)	WSSS Section	Max Mark	Criterion          A	Total Mark	40.00
A1	命名实体提取（NER）任务										
        M	人工智能职业基础					10.00			
                    ①选择题共5题，错一题扣2分						
        M	转换文件格式					1.00			
                    ①未正确实现文本分割逻辑，扣0.5分						
                    ②未正确处理句子边界和剩余文本，扣0.5分						
        M	根据JSON格式数据划分BIOES标注数据					1.00			
                    ①未正确处理单字实体标注（S标签），扣0.5分						
                    ②未正确处理多字实体标注（B-I-E标签），扣0.5分						
        M	根据特定标点符号分割文本为句子列表					1.00			
                    ①未正确提取段落和句子信息，扣0.5分						
                    ②未正确调用process_entity函数生成BIOES标注，扣0.5分						
        M	完成格式转变					1.00			
                    ①未正确遍历目录中的JSON文件，扣0.5分						
                    ②未正确调用convert_to_bioes函数并合并结果，0.5分						
        M	构建模型输入数据					1.50			
                    ①未正确读取bioes文件以解析词和标签，扣0.5分						
                    ②未将词和标签添加至列表，两项各占0.25分，共0.5分						
                    ③未正确生成word2id和tag2id映射，两项各占0.25分，共0.5分						
        M	基于条件随机场（CRF）的序列标注模型					2.00			
                    ①实现训练函数，未提取特征，扣0.5分，未实现训练模型，扣0.5分						
                    ②实现测试函数，未提取特征，扣0.5分，未实现预测，扣0.5分						
        M	基于双向LSTM（BiLSTM）的序列标注模型					2.50			
                    ①未正确定义词向量层，扣0.5分						
                    ②未正确定义BiLSTM层参数，扣0.5分						
                    ③未正确定义输出全连接层，扣0.5分						
                    ④未正确实现forward函数，未全对均扣1分						
        M	基于隐马尔可夫模型（HMM）的序列标注模型					4.00			
                    ①未正确估计状态转移矩阵，未全对均扣0.5分						
                    ②未正确估计观测概率矩阵，未全对均扣0.5分						
                    ③未正确估计初始状态概率分布，未全对均扣1分						
                    ④未正确实现维特比算法递推公式，每空扣0.5分，共1分						
                    ⑤未正确找到最优路径终点，扣0.5分						
                    ⑥未正确实现路径回溯，扣0.5分						
        M	基于双向LSTM和条件随机场（BiLSTM-CRF）的序列标注模型					4.50			
                    ①未正确定义转移矩阵，扣0.5分						
                    ②未正确计算发射分数和转移分数，每空扣0.5分，共1分						
                    ③未正确实现Viterbi解码前向递推，扣1分						
                    ④未正确补全BiLSTM-CRF模型定义，扣0.5分						
                    ⑤未正确定义优化器，扣0.5分						
                    ⑥未正确实现前向传播，扣0.5分；未正确实现损失计算，扣0.5分，共1分						
        M	模型训练和测试					3.00			
                    ①未实现CRF模型训练扣0.5分，未实现保存和测试各扣0.25分，共1分						
                    ②未实现HMM模型训练扣0.5分，未实现保存和测试各扣0.25分，共1分						
                    ③未实现BiLSTM模型训练扣0.5分，未实现保存和测试各扣0.25分，共1分						
A2	三元组提取（KGC）任务										
        M	转换文件格式					2.00			
                    ①未正确解析JSONL文件格式，扣0.5分						
                    ②未正确提取text字段内容，扣0.5分						
                    ③未正确提取和序列化triple_list字段，各扣0.5分，共1分						
        M	填写文档路径参数					0.50			
                    ①正确填写源文件路径，并且填写了保存位置参数 （0.5分）						
        J	构造few shot示例					1.00			
                    ①未正确说明示例数据格式，扣0.5分						
                    ②未正确构造示例字典结构，扣0.5分						
        J	构造prompt提示词					1.50			
                    ①未正确设计系统角色，扣0.5分						
                    ②未清晰进行任务描述，扣0.5分						
                    ③未正确规定输出格式，扣0.5分						
        M	精度评测					1.50			
                    ①未正确计算tp、fp、fn，各扣0.5分，共1.5分						
        M	知识图谱构建					2.00			
                    ①未正确添加实体关系边到图中，扣0.5分						
                    ②实现基于关系类型的实体邻居查找，未全对均扣1分						
                    ③未正确实现知识图谱中的最短路径查找，扣0.5分						
Sub Criteria ID	Sub Criteria Name or Description	Aspect Type     M = Meas  J = Judg	Aspect - Description	Judg Score	Extra Aspect Description (Meas )                                    OR                                                                                                                     Judgement Score Description (Judg only)	Requirement      or Nominal           Size (Meas Only)	WSSS Section	Max Mark	Criterion          B	Total Mark	35.00
B1	病症分类任务										
        M	图像数据增强方法定义					3.00			
                    ①未使用random中提供的接口获取随机数扣0.5分，未判断是否对图像进行数据增强扣0.5分，最多扣1分						
                    ②计算随机擦除区域的宽和高，每空0.5分，最多扣1分						
                    ③未使用_get_pixels()生成的随机像素值替换随机擦除区域原本的像素值，扣1分						
        M	图像数据预处理					4.50			
                    ①未正确设置训练集预处理方法扣0.5分，未正确设置测试集预处理方法扣0.5分，最多扣1分						
                    ②剪裁中心点周围区域、数据水平翻转、数据随机旋转、和①中训练集相同的预处理操作，以上四项未完成一项扣0.25分，最多扣1分						
                    ③读取训练集和测试集的数据同时对数据进行预处理，训练集和测试集的读取和预处理各0.25分，最多扣0.5分						
                    ④未再次读取训练集并将其作为增广数据进行预处理，扣0.5分						
                    ⑤未正确创建训练集的Dataloader对象扣0.25分，未正确创建测试集的Dataloader对象扣0.25分，最多扣0.5分						
        M	损失函数构造					1.50			
                    ①未使用Softmax函数处理输入的预测结果扣0.5分						
                    ②未使用one_hot函数将真值标签转化为独热编码扣0.25分，未将数据类型转为float扣0.25分。最多扣0.5分						
                    ③未使用求和函数计算损失值扣0.5分						
        M	分类模型设置					2.00			
                    ①未调用分类模型扣0.5分						
                    ②未获取未输入扣0.5分，未设置模型全连接层的输入和输出维度扣0.5分，未将模型移动到和数据相同的设备上扣0.5分						
                    ③未调用交叉熵损失函数扣0.5分						
                    ④未选择优化器扣0.5分						
        M	模型训练和验证函数构造					3.00			
                    ①未将模型设置为训练模式扣0.5分						
                    ②未计算当前batch的损失函数扣0.5分，未当前batch的梯度初始化为零扣0.5分，未反向传播求梯度扣分，未更新所有参数扣						
                    ③未计算该epoch的平均loss扣0.5分						
                    ④未将模型设置为评估模式扣0.5分						
                    ⑤未判断并增加准确预测数扣0.5分						
                    ⑥未准确计算模型测试准确率扣0.5分						
                                            
        M	模型测试结果可视化					3.00			
                    ①单通道灰度图转化为三通道的彩色图像，每空0.25分，最多扣1分						
                    ②未进行数据预处理扣0.5分						
                    ③未将模型设置为评估模式扣0.5分						
                    ④未预处理读取的图像扣0.1分，未将图像转化为三通道彩色图像扣0.1分，未扩大图像维度扣0.3分，最多扣0.5分						
                    ⑤未按要求设置子图展示预测结果扣0.3分，未更新位置标识idx扣0.2分，最多扣0.5分						
        M	模型分类性能可视化					3.00			
                    ①未读取测试集数据并进行预处理扣0.25分，未按要求创建测试集的Dataloader对象扣0.25分，最多扣0.5分						
                    ②未将模型设置为评估模式扣0.5分						
                    ③未替换model_feat的全连接层扣0.25分，未将该模型设置为评估模式扣0.25分，最多扣0.5分						
                    ④未将列表拼接为一个二维张量扣0.5分						
                    ⑤未按要求定义TSNE扣0.5分						
                    ⑥未返回转换结果扣0.5分						
B2	药品检测任务										
        M	数据增强方法定义					2.00			
                    ①未根据指示初始化画布扣0.5分						
                    ②未计算画布坐标系下第一张图所在区域位置的左上角点与右下角点坐标扣0.25分						
                    ③未计算画布坐标系下第二张图所在区域位置的左上角点与右下角点坐标扣0.25分						
                    ④未计算画布坐标系下第三张图所在区域位置的左上角点与右下角点坐标扣0.25分						
                    ⑤未计算画布坐标系下第四张图所在区域位置的左上角点与右下角点坐标扣0.25分						
                    ⑥未将原图像裁剪后贴入对应画布中扣0.5分						
        M	数据集预处理定义					1.00			
                    ①未使用Compose函数将mosaic和随机扰动整合成前处理方法扣1分						
        M	可视化图像数据获取及标注					2.00			
                    ①未通过摄像头获取图像扣1分						
                    ②未利用数据标注平台进行标注扣1分						
        M	模型构建					2.00			
                    ①未按图示设置P1、P2、P3、P4、P5层的module和args，每层占0.1分，最多扣0.5分						
                    ②未按要求设置上采样层，两层各占0.25分，最多扣0.5分						
        M	Distribution Focal Loss函数定义					2.00			
                    ①按要求分别计算预测分布与左右目标值交叉熵损失并求和，每空0.25分，最多扣2分						
        M	模型训练和预测函数					2.25			
                    ①未加载预训练好的模型扣0.25分						
                    ②未正确设置训练函数最多扣0.75分，每空占0.25分						
                    ③未加载刚训练好的模型扣0.25分						
                    ④未正确构造vis.yaml扣1分，每空0.25分						
        M	检测结果可视化					0.75			
                    ①未加载刚训练好的模型扣0.25分						
                    ②未读取测试图像扣0.25分						
                    ③未读取当前路径扣0.25分						
        M	平台展示药品检测结果					3.00			
                    ①RGB显示为绿色，数码管展示类别的ap75结果，灵巧手比出剪刀，每项占0.5分，最多扣1.5分						
                    ②RGB显示为红色，数码管展示类别的ap75结果，灵巧手比出拳头，每项占0.5分，最多扣1.5分						
Sub Criteria ID	Sub Criteria Name or Description	Aspect Type     M = Meas  J = Judg	Aspect - Description	Judg Score	Extra Aspect Description (Meas )                                    OR                                                                                                                     Judgement Score Description (Judg only)	Requirement      or Nominal           Size (Meas Only)	WSSS Section	Max Mark	Criterion          C	Total Mark	25.00
C1	药品知识问答										
        M	加载模型制作训练集					8.50			
                    ①设置max_size参数 (0.5分) ,实现图片缩放逻辑 (1分)						
                    ② 正确识别正面图片 (0.5分)，正确调用resize函数 (0.5分)						
                    ③ 构造合适的user_instruction (0.5分)，正确构造content内容 (0.5分)，设置合适的max_new_tokens (0.5分)，正确提取模型输出 (0.5分)						
                    ④ 正确构造images字段 (0.5分)，设置正确的输出路径 (0.5分)，正确保存JSON数据 (0.5分)						
                    ⑤ 正确配置dataset_info.json (2分)，正确放置train.json文件 (0.5分)						
        M	模型训练					4.00			
                    ① YAML配置文件参数设置，每个参数0.5分，共3.5分						
                    ② 正确执行模型训练，得到结果，0.5分						
        M	模型导出					1.00			
                    ①正确配置导出参数 ，0.5分						
                    ② 正确执行模型导出，得到结果，0.5分						
        M	模型能力测评					3.50			
                    ①准确率评分标准：Accuracy > 0.85 (3.5分)；0.80 < Accuracy ≤ 0.85 (3分)；0.75 < Accuracy ≤ 0.80 (2.5分)；0.70 < Accuracy ≤ 0.75 (2分)；0.65 < Accuracy ≤ 0.70 (1.5分)；0.60 < Accuracy ≤ 0.65 (1分)；Accuracy ≤ 0.60 (0分)						
        M	模型量化与验证					1.50			
                    ①正确执行量化过程，得到量化结果1分						
                    ②正确用模型进行推理验证0.5分						
C2	边缘设备真实场景实践										
        M	模型转换部署					4.00			
                    ①正确实现模型转换为onnx模型，1.5分						
                    ②视觉部分转换为rknn模型，正确填写参数 (0.5分)						
                    ③正确实现模型转换为rkllm模型，1分						
                    ④模型部署，并且正确配置，能够进行推理 (1分)						
        M	板端测试					2.50			
                    ①每题正确完成 (0.5分 × 5题)						
                                    Competition	Total Mark	0.00
