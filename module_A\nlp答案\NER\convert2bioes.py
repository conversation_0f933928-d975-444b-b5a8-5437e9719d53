import os
import json
from typing import List, Dict, Any, <PERSON><PERSON>

def split_sentences(text: str) -> List[str]:
    """
    根据特定标点符号分割文本为句子列表
    
    Args:
        text: 待分割的文本
    
    Returns:
        分割后的句子列表
    """
    sentence_endings = "。！？"
    sentences = []
    start = 0
    #---------------------------------------1------------------------------------------
    for i in range(len(text)):
        if text[i] in sentence_endings:
            sentences.append(text[start:i + 1])
            start = i + 1
    if start < len(text):
        sentences.append(text[start:])
    #---------------------------------------1------------------------------------------
    return sentences

def process_entity(
    sentence: str, 
    sent_text: str, 
    entity: Dict[str, Any],
    tags: List[str]
) -> None:
    """
    处理单个实体，生成对应的BIOES标签
    
    Args:
        sentence: 当前处理的句子
        sent_text: 包含实体的完整文本
        entity: 实体信息字典
        tags: 标签列表，将被修改
        B: 实体的开始
        E: 实体的结束
        I: 实体的内部
        S: 单字符实体
        O: 非实体
    """
    start_idx = entity.get('start_idx', 0) - sent_text.index(sentence)
    end_idx = entity.get('end_idx', 0) - sent_text.index(sentence)
    entity_type = entity.get('entity_type', '')
    
    # 检查实体索引是否在当前句子范围内
    if start_idx < 0 or end_idx > len(sentence):
        return
    #------------------------------------2------------------------------
    if start_idx == end_idx - 1:
        # 单字实体
        tags[start_idx] = f'S-{entity_type}'
    else:
        # 多字实体
        tags[start_idx] = f'B-{entity_type}'
        for i in range(start_idx + 1, end_idx - 1):
            tags[i] = f'I-{entity_type}'
        tags[end_idx - 1] = f'E-{entity_type}'
    #------------------------------------2------------------------------

def convert_to_bioes(json_data: Dict[str, Any]) -> List[str]:
    """
    将JSON格式的数据转换为BIOES标注格式
    
    Args:
        json_data: 包含段落和实体信息的JSON数据
    
    Returns:
        BIOES格式的行列表
    """
    bioes_lines = []
    
    for paragraph in json_data.get('paragraphs', []):
        paragraph_text = paragraph.get('paragraph', '')
        sentences = split_sentences(paragraph_text)
        
        for sentence in sentences:
            # 初始化标签列表
            tags = ['O'] * len(sentence)
            #------------------------------------3------------------------------
            for sent_info in paragraph.get('sentences', []):
                sent_text = sent_info.get('sentence', '')
                if sentence in sent_text:
                    entities = sent_info.get('entities', [])
                    for entity in entities:
                        process_entity(sentence, sent_text, entity, tags)
            
            # 生成BIOES格式的行，忽略空格、回车和制表符
            for char, tag in zip(sentence, tags):
                if char.strip():
                    bioes_lines.append(f'{char} {tag}')
            bioes_lines.append('')  # 句子之间用空行隔开
            #------------------------------------3------------------------------
    
    return bioes_lines

def process_directory(input_dir: str) -> List[str]:
    """
    处理目录中的所有JSON文件，转换为BIOES格式
    
    Args:
        input_dir: 输入目录路径
    
    Returns:
        所有文件的BIOES格式行列表
    """
    all_bioes_lines = []
    
    # 遍历目录中的所有JSON文件
    for filename in os.listdir(input_dir):
        if filename.endswith('.json'):
            file_path = os.path.join(input_dir, filename)
            try:#----------------------------------------------4--------------------------------------
                with open(file_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                    bioes_lines = convert_to_bioes(json_data)
                    all_bioes_lines.extend(bioes_lines)
                #----------------------------------------------4--------------------------------------
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    return all_bioes_lines

def main():
    """
    主函数，处理三个数据集目录并生成对应的BIOES文件
    """
    base_data_dir = './data/'
    datasets = ['train', 'dev', 'test']
    
    for dataset in datasets:
        input_dir = os.path.join(base_data_dir, dataset)
        output_file = os.path.join(base_data_dir, f'{dataset}.bioes')
        
        if not os.path.exists(input_dir):
            print(f"目录 {input_dir} 不存在，跳过处理")
            continue
        
        print(f"正在处理 {dataset} 数据集...")
        bioes_lines = process_directory(input_dir)
        
        # 将结果保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            for line in bioes_lines:
                f.write(line + '\n')
        
        print(f"{dataset} 数据集处理完成，BIOES格式文件已保存到 {output_file}")
        print(f"共处理 {len(bioes_lines)} 行数据\n")

if __name__ == "__main__":
    main()    