from modelscope import AutoModelFor<PERSON>ausalLM, AutoTokenizer
import torch
import ast
import os
import sys
from tqdm import tqdm

class QwenModel:
    def __init__(self):
        """
        初始化Qwen模型
        参数：
            device: 计算设备 (cuda/cpu)
            torch_dtype: 张量精度
            model_path: 模型所在路径(默认Qwen3-0.6B,可修改为Qwen3-4B)
        """
        self.device = "cuda"
        self.torch_dtype = torch.bfloat16
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.model_path = os.path.abspath(os.path.join(script_dir, "../../Qwen3-4B"))

        # 延迟加载组件
        self._model = None
        self._tokenizer = None

    @property
    def tokenizer(self):
        if self._tokenizer is None:
            self._tokenizer = AutoTokenizer.from_pretrained(self.model_path)
        return self._tokenizer
    
    @property
    def model(self):
        if self._model is None:
            #------------------------------------------------------1---------------------------------------------
            self._model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
            ).to(self.torch_dtype).to(self.device).eval()
            #------------------------------------------------------1---------------------------------------------
        return self._model

    def _is_triplet_extraction_task(self, user_input, few_shot, custom_prompt):
        """
        使用模型评估输入是否为三元组提取相关任务
        返回True表示是三元组提取任务，False表示不是
        """
        try:
            # 构造评估消息
            total_prompt = custom_prompt + " 如下是一个参考示例: " + str(few_shot)
            evaluation_messages = [
                {
                    "role": "system",
                    "content": "你是一个任务分类器。请判断用户的输入是否是要求从文本中提取三元组（实体关系对）的任务。"
                              "如果是三元组提取任务，只回复'yes'。如果不是（如代码生成、问答、翻译等其他任务），只回复'no'。"
                              "只能回复'yes'或'no'，不要有其他内容。"
                },
                {
                    "role": "user",
                    "content": f"用户输入文本：{user_input}\n用户自定义提示：{total_prompt}\n这是三元组提取任务吗？"
                }
            ]

            # 生成评估输入
            eval_text = self.tokenizer.apply_chat_template(
                evaluation_messages,
                tokenize=False,
                add_generation_prompt=True,
                enable_thinking=False
            )

            eval_inputs = self.tokenizer([eval_text], return_tensors="pt").to(self.model.device)

            # 生成评估结果
            with torch.no_grad():
                eval_outputs = self.model.generate(
                    **eval_inputs,
                    max_new_tokens=10,  # 只需要很短的回复
                    temperature=0.1,
                    do_sample=False
                )

            # 解码评估结果
            eval_response_ids = eval_outputs[0][len(eval_inputs.input_ids[0]):].tolist()
            eval_response = self.tokenizer.decode(eval_response_ids, skip_special_tokens=True).strip().lower()

            print(f"*** 安全评估结果: {eval_response}")

            # 清理GPU内存
            del eval_inputs, eval_outputs
            torch.cuda.empty_cache()

            # 判断是否为三元组提取任务
            return "yes" in eval_response

        except Exception as e:
            print(f"安全评估失败: {e}")
            # 评估失败时，为安全起见，拒绝执行
            return False

    def extract_triples(self, user_input, few_shot, custom_prompt):
        try:
            # 使用传入的few shot示例
            #------------------------------------------------------2---------------------------------------------
            # few_shot参数必须由调用方提供，不使用默认值
            #------------------------------------------------------2---------------------------------------------
            # 构造prompt
            #------------------------------------------------------3---------------------------------------------
            messages = [
                # 系统消息：定义任务
                {"role": "system",
                "content": "你是一个从文本中提取三元组的助手。请严格按照要求的格式输出三元组。"
                },
                # 助手消息：使用自定义prompt和few shot示例
                {"role": "assistant",
                "content": custom_prompt + " 如下是一个参考示例: " + str(few_shot)
                }
            ]
            #------------------------------------------------------3---------------------------------------------
            # 测试三元组提取示例
            messages.append({"role": "user", "content": "user input text:" + user_input})

            # 生成模型输入
            text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True,
            enable_thinking=False
            )
            
            # 生成参数设置
            model_inputs = self.tokenizer([text], return_tensors="pt").to(self.model.device)

            # 三元组提取
            #------------------------------------------------------4---------------------------------------------
            generated_ids = self.model.generate(
                **model_inputs,
                max_new_tokens=1024,
                temperature=0.1,
            )
            #------------------------------------------------------4---------------------------------------------
            
            output_ids = generated_ids[0][len(model_inputs.input_ids[0]):].tolist() 

            try:
                index = len(output_ids) - output_ids[::-1].index(151668)
            except ValueError:
                index = 0

            assistant_response = self.tokenizer.decode(output_ids[index:], skip_special_tokens=True).strip("\n")

            print(f"*** 输出结果: {assistant_response}")
            return assistant_response

        finally:
            if 'model_inputs' in locals():
                del model_inputs
            if 'generated_ids' in locals():
                del generated_ids
            torch.cuda.empty_cache()
    

def main(text_path, result_path, few_shot, prompt):
    """
    主函数：执行三元组提取任务
    参数：
        text_path: 输入文本文件路径
        result_path: 输出结果文件路径
        few_shot: few shot示例
        prompt: 自定义prompt
    """
    # 读取输入文本文件
    with open(text_path, 'r', encoding='utf-8') as file:
        text = [line.strip() for line in file]

    print(f"输入文本个数为：{len(text)}")

    model = QwenModel()
    # 安全检查：使用模型评估是否为三元组提取任务
    if not model._is_triplet_extraction_task(text[0], few_shot=few_shot, custom_prompt=prompt):
        return "我只能执行三元组提取任务。"
    else:
        # 打开结果文件用于写入
        with open(result_path, 'w', encoding='utf-8') as result_file:
            # 知识图谱构建
            for i in tqdm(range(len(text)), desc="调用qwen3提取三元组:"):
                #------------------------------------------------------5---------------------------------------------
                triples = model.extract_triples(text[i], few_shot=few_shot, custom_prompt=prompt)
                result_file.write(triples + '\n')  # 写入文件并换行
                #------------------------------------------------------5---------------------------------------------

if __name__ == "__main__":
    # 这部分在编译后的pyc文件中不会被直接调用
    print("请使用run_llm.py脚本来运行此模块")
