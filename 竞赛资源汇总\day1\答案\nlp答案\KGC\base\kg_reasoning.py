import json
import ast
from collections import defaultdict
from knowledge_graph import KnowledgeGraph

class KGReasoning:
    """知识图谱推理类"""
    
    def __init__(self, kg: KnowledgeGraph):
        self.kg = kg
    
    def find_disease_symptoms(self, disease_name):
        """
        查找疾病的所有症状
        """
        #------------------------------------------------------4---------------------------------------------
        # 基于知识图谱查找疾病的所有临床表现
        symptoms = []
        neighbors = self.kg.get_entity_neighbors(disease_name, "临床表现")

        for neighbor, relation in neighbors:
            if relation == "临床表现":
                symptoms.append(neighbor)

        return symptoms
        #------------------------------------------------------4---------------------------------------------
    
    def find_similar_diseases(self, target_disease):
        """
        基于症状相似性查找相似疾病
        """
        #------------------------------------------------------5---------------------------------------------
        # 基于症状相似性实现疾病推理
        target_symptoms = set(self.find_disease_symptoms(target_disease))
        similar_diseases = []

        # 遍历图中所有节点，找出其他疾病
        for node in self.kg.graph.nodes():
            if node != target_disease:
                node_symptoms = set(self.find_disease_symptoms(node))

                # 计算症状重叠度（Jaccard相似度）
                if target_symptoms and node_symptoms:
                    overlap = len(target_symptoms & node_symptoms)  # 交集
                    union = len(target_symptoms | node_symptoms)    # 并集
                    similarity = overlap / union   # 相似度计算

                    if similarity > 0.1:  # 相似度阈值
                        similar_diseases.append((node, similarity, overlap))

        # 按相似度排序
        similar_diseases.sort(key=lambda x: x[1], reverse=True)
        return similar_diseases[:5]  # 返回前5个最相似的
        #------------------------------------------------------5---------------------------------------------
    
    def generate_reasoning_report(self, entity):
        """
        生成实体的推理报告
        """
        report = {
            "entity": entity,
            "direct_relations": [],
            "similar_entities": [],
            "potential_inferences": []
        }
        
        # 直接关系
        neighbors = self.kg._____
        report["direct_relations"] = neighbors
        
        # 相似实体（如果是疾病）
        if any("疾病" in str(neighbor) or "癌" in str(neighbor) for neighbor, _ in neighbors):
            similar = self.find_similar_diseases(entity)
            report["similar_entities"] = similar
        
        return report

if __name__ == "__main__":
    # 加载知识图谱
    kg = KnowledgeGraph()
    kg.build_from_triples("../data/target.txt")
    
    # 创建推理引擎
    reasoner = KGReasoning(kg)
    
    # 示例推理
    print("=== 知识图谱推理示例 ===")
    
    # 查找疾病症状
    symptoms = reasoner.find_disease_symptoms("小细胞肺癌")
    print(f"小细胞肺癌的症状: {symptoms}")
    
    # 查找相似疾病
    similar = reasoner.find_similar_diseases("小细胞肺癌")
    print(f"与小细胞肺癌相似的疾病: {similar}")
    
    # 生成推理报告
    report = reasoner.generate_reasoning_report("小细胞肺癌")
    print(f"推理报告: {json.dumps(report, ensure_ascii=False, indent=2)}")
