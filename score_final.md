各难度题目分数比重
为了保证赛项顺利进行，设置70%的基础题目考察选手的工程技术水平，设置30%较难的内容用于区分选手能力。
基础内容（70%）
每个步骤作为一个大得分点，分值区间在1-10分；
1-2分题目通常为简单的数据预处理步骤，包括文件格式转换、路径配置等内容；3-4分题目通常为模型构建和训练步骤，包括模型架构定义、损失函数实现等内容；5-8分题目通常为复杂的算法实现，包括序列标注模型、数据增强算法等内容；10分题目通常为理论基础考察，如选择题等。
每个步骤中需实现多个功能，每个功能作为得分细项，分值区间在0.25-2分。
0.25分题目通常作为一个功能中较为简单的最小部分，如基础参数设置等；0.5分题目通常为实现一个简单功能，如单个函数调用、简单计算等；1分题目通常为实现一个正常难度功能，如完整的数据处理流程等；1.5-2分题目通常为实现较难或流程较为复杂的功能，如完整实现算法核心逻辑等。

能力区分内容（30%）
作为附加步骤穿插在主要流程中，分值区间在1-4分；
主要内容为复杂算法实现，如HMM模型的维特比算法、BiLSTM-CRF模型实现等。
作为附加功能穿插在步骤中，分值区间在1-3.5分。
1-2分功能主要为给出提示且实现复杂度一般的完整功能，如数据增强算法实现、模型可视化等；3-3.5分功能主要为给出提示较少且实现复杂度较高的完整功能，如模型性能评估、边缘设备部署等。

各模块题目分数占比
为了保证题目的难度平衡，对三个模块的题目数量、分数占比进行了分配。
模块A：自然语言处理技术应用（40%）
模块B：计算机视觉技术应用（35%）
模块C：综合工程技术应用（25%）

各技术能力维度分数占比
为了保证题目的难度平衡，对算法理论理解、工程实现能力、系统集成应用的题目数量、分数占比进行了分配（统计结果）。
算法理论理解（30%左右）
工程实现能力（50%左右）
系统集成应用（20%左右）
