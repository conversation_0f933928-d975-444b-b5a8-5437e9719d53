from sklearn_crfsuite import CRF

from .util import sent2features


class CRFModel(object):
    def __init__(self,
                 algorithm='lbfgs',
                 c1=0.1,
                 c2=0.1,
                 max_iterations=100,
                 all_possible_transitions=False
                 ):

        self.model = CRF(algorithm=algorithm,
                         c1=c1,
                         c2=c2,
                         max_iterations=max_iterations,
                         all_possible_transitions=all_possible_transitions)

    def train(self, sentences, tag_lists):
        #-----------------------------------------------1--------------------------------------------
        features = [sent2features(s) for s in sentences]
        self.model.fit(features, tag_lists)
        #-----------------------------------------------1--------------------------------------------

    def test(self, sentences):
        #-----------------------------------------------2--------------------------------------------
        features = [sent2features(s) for s in sentences]
        pred_tag_lists = self.model.predict(features)
        #-----------------------------------------------2--------------------------------------------
        return pred_tag_lists
