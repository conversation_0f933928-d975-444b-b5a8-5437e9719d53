# 使用带CUDA的Ubuntu基础镜像
FROM nvidia/cuda:11.8.0-devel-ubuntu22.04

# 安装基础工具和Python环境
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    python3.10 \
    python3-pip \
    git \
    wget \
    vim \
    python3.10-venv \ 
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /workspace
COPY . /workspace

# 安装PyTorch和依赖
RUN pip install --no-cache-dir torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 \
    --index-url https://download.pytorch.org/whl/cu118 && \
    pip uninstall -y numpy && \
    pip install --no-cache-dir "numpy<2"

# 安装requirements.txt中的依赖
RUN pip install --no-cache-dir -r requirements.txt

# 验证CUDA
RUN python3 -c "import torch; print('CUDA available:', torch.cuda.is_available())"

# 设置默认命令（VS Code 会自动覆盖）
CMD ["sleep", "infinity"]  # 保持容器运行