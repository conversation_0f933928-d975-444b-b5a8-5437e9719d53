from os.path import join
from codecs import open


def build_map(lists):
    maps = {}
    for list_ in lists:
        for e in list_:
            if e not in maps:
                maps[e] = len(maps)
    return maps

def build_corpus(split, make_vocab=True, data_dir="./data"):
    """读取数据"""
    assert split in ['train', 'dev', 'test']

    word_lists = []
    tag_lists = []
    with open(join(data_dir, split+".bioes"), 'r', encoding='utf-8') as f:
        word_list = []
        tag_list = []
        # 读取BIOES格式文件并解析词和标签
        for line in f:
            if line != '\n':
                # <1>分割每行的词和标签
                word, tag = _____
                word_list.append(word)
                tag_list.append(tag)
            elif (word_list!= []) and (tag_list!= [] ):
                # <1>添加到列表
                word_lists._____
                tag_lists._____
                word_list = []
                tag_list = []

    # 构建词汇表和标签映射
    # 如果make_vocab为True，还需要返回word2id和tag2id
    if make_vocab:
        # <2>构建word2id映射
        word2id = _____
        # <2>构建tag2id映射
        tag2id = _____
        return word_lists, tag_lists, word2id, tag2id
    else:
        return word_lists, tag_lists