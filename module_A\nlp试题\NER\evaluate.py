import time
from collections import Counter

from models.hmm import HMM
from models.crf import CRFModel
from models.bilstm_crf import BILSTM_Model
from utils import save_model, flatten_lists
from evaluating import Metrics


def crf_train_eval(train_data, test_data, remove_O=False):

    # 训练CRF模型
    train_word_lists, train_tag_lists = train_data
    test_word_lists, test_tag_lists = test_data
    
    # CRF模型训练、保存和测试
    crf_model = CRFModel()
    # <1>训练模型 
    crf_model._____
    # <1>保存模型
    save_model(
        _____
    )
    
    # <1>测试模型
    pred_tag_lists = crf_model._____

    metrics = Metrics(test_tag_lists, pred_tag_lists, remove_O=remove_O)
    #------------------------------------------------------1---------------------------------------------
    
    metrics.report_scores()
    metrics.report_confusion_matrix()

    return pred_tag_lists


def hmm_train_eval(train_data, test_data, word2id, tag2id, remove_O=False):
    """训练并评估hmm模型"""
    # 训练HMM模型
    train_word_lists, train_tag_lists = train_data
    test_word_lists, test_tag_lists = test_data

    # HMM模型训练、保存和测试
    hmm_model = HMM(len(tag2id), len(word2id))
    # <2>训练模型
    hmm_model._____
    # <2>保存模型
    save_model(
        _____
    )

    # <2>测试模型
    pred_tag_lists = hmm_model._____

    metrics = Metrics(test_tag_lists, pred_tag_lists, remove_O=remove_O)
        
    metrics.report_scores()
    metrics.report_confusion_matrix()

    return pred_tag_lists


def bilstm_train_and_eval(train_data, dev_data, test_data,
                          word2id, tag2id, crf=True, remove_O=False):
    train_word_lists, train_tag_lists = train_data
    dev_word_lists, dev_tag_lists = dev_data
    test_word_lists, test_tag_lists = test_data

    start = time.time()
    vocab_size = len(word2id)
    out_size = len(tag2id)
    # BiLSTM模型训练、保存和测试
    bilstm_model = BILSTM_Model(vocab_size, out_size, crf=crf)
    # <3>训练模型
    bilstm_model._____
    model_name = "bilstm_crf" if crf else "bilstm"
    # <3>保存模型
    save_model(
        
    )

    print("训练完毕,共用时{}秒.".format(int(time.time()-start)))
    print("评估{}模型中...".format(model_name))
    # <3>测试模型
    pred_tag_lists, test_tag_lists = bilstm_model._____

    metrics = Metrics(test_tag_lists, pred_tag_lists, remove_O=remove_O)
    
    metrics.report_scores()
    metrics.report_confusion_matrix()

    return pred_tag_lists


def ensemble_evaluate(results, targets, remove_O=False):
    """ensemble多个模型并将结果写入文件"""
    for i in range(len(results)):
        results[i] = flatten_lists(results[i])

    pred_tags = []
    for result in zip(*results):
        ensemble_tag = Counter(result).most_common(1)[0][0]
        pred_tags.append(ensemble_tag)

    targets = flatten_lists(targets)
    assert len(pred_tags) == len(targets)

    # 创建评估指标对象
    metrics = Metrics(targets, pred_tags, remove_O=remove_O)
    metrics.report_scores()
    metrics.report_confusion_matrix()
    
    return metrics
