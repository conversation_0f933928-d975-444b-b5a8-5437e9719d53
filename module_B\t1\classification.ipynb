import os
import random
import numpy as np
import torch.optim as optim
import torch
import torch.nn as nn
import torch.nn.parallel
import torch.nn.functional as F
import torch.utils.data
import torch.utils.data.distributed
import torchvision.transforms as transforms
import torchvision.datasets as datasets
import torchvision.models
from torch.autograd import Variable
import math

# 设置全局参数
modellr = 1e-4
BATCH_SIZE = 12
EPOCHS = 10
DEVICE = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

# 实现RandomErasing数据增强方法
# 生成用于替换擦除区域的随机像素值
def _get_pixels(patch_size, dtype=torch.float32, device=DEVICE):
    return torch.empty((patch_size[0], 1, 1), dtype=dtype, device=device).normal_()
    
class RandomErasing:
   def __init__(self, probability=0.5, min_area=0.02, max_area=1/3, min_aspect=0.3, max_aspect=None, min_count=1, max_count=None, num_splits=0, device=DEVICE):
       # 随机擦除概率
       self.probability = probability
       # 要擦除矩形区域的面积
       self.min_area = min_area
       self.max_area = max_area
       # 擦除矩形区域的长宽比
       max_aspect = max_aspect or 1 / min_aspect
       self.log_aspect_ratio = (math.log(min_aspect), math.log(max_aspect))
       self.min_count = min_count
       self.max_count = max_count or min_count
       self.num_splits = num_splits
       self.device = device
    
   def _erase(self, img, chan, img_h, img_w, dtype):
       # img为输入图像，chan为输入图像的通道数，img_h和img_w分别为输入图像的高和宽，dtype为数据类新
       # <1> 使用random获取随机数，按照题目中Algorithm1伪代码的描述，判断是否对图像进行RandomErasing
       if _______________________:
           return
       area = img_h * img_w
       count = self.min_count if self.min_count == self.max_count else random.randint(self.min_count, self.max_count)
       for _ in range(count):
           for attempt in range(10):
               target_area = random.uniform(self.min_area, self.max_area) * area / count
               aspect_ratio = math.exp(random.uniform(*self.log_aspect_ratio))
               # <2> 根据公式计算擦除矩形区域的宽高，要求结果四舍五入取整，且数据类型为int
               h = _______________________
               w = _______________________
               if w < img_w and h < img_h:
                   top = random.randint(0, img_h - h)
                   left = random.randint(0, img_w - w)
                   # <3> 用随机值替换原本的像素值
                   _____________________________________________________________________
                   break
                    
   def __call__(self, input):
       if len(input.size()) == 3:
           self._erase(input, *input.size(), input.dtype)
       else:
           batch_size, chan, img_h, img_w = input.size()
           batch_start = batch_size // self.num_splits if self.num_splits > 1 else 0
           for i in range(batch_start, batch_size):
               self._erase(input[i], chan, img_h, img_w, input.dtype)
       return input

   def __repr__(self):
       fs = self.__class__.__name__ + f'(p={self.probability}'
       fs += f', count=({self.min_count}, {self.max_count}))'
       return fs

# <1> 数据预处理操作定义
# 训练集预处理
transform = _______________________
# 测试集预处理
transform_test = _______________________

print("transform: ", transform)
print("transform_test: ", transform_test)

# <2> 增广数据所需预处理操作定义
transform_a = _______________________

print("transform_a: ", transform_a)

# 读取数据并同时进行预处理
# <3> 读取训练集数据并同时进行预处理
dataset_train = _______________________
print("扩充前训练集样本数量: ", len(dataset_train.imgs))
# <3> 读取测试集数据并同时进行预处理
dataset_test = _______________________

# 使用数据增广操作“transform_a”扩充训练集
# <4>对训练集样本进行增强,并保存为dataset_train_a数据集
dataset_train_a = _______________________
# 调用接口使用dataset_train_a数据集的数据扩充训练集dataset_train数据
dataset_train.class_to_idx.update(dataset_train_a.class_to_idx)
dataset_train.imgs.extend(dataset_train_a.imgs)

print("训练集类别标签: ", dataset_train.class_to_idx)
print("扩充后训练集样本数量: ", len(dataset_train.imgs))
print("测试集类别标签: ", dataset_test.class_to_idx)
 
# <5> 使用torch.utils.data.DataLoader创建训练集和测试集的Dataloader对象
# 要求设置Dataloader的dataset 、batch_size和shuffle三个参数，且仅训练时在每个epoch重新打乱数据
train_loader = ______________________________________________
test_loader = ______________________________________________

# 定义交叉熵损失
class CrossEntropyLoss(nn.Module):
    def __init__(self) -> None:
        super(CrossEntropyLoss, self).__init__()

    def forward(self, pred, target):
        # <1> 使用torch中预设的Softmax函数处理输入的预测结果pred
        pred = _______________________
        # <2> 使用torch.nn.functional中预设的one_hot函数将真值标签转化为独热编码，并将数据类型转为float
        one_hot = _______________________
        log = torch.log(pred)
        # <3> 根据公式，使用torch中预设的求和函数计算损失值
        loss = _______________________
    
        return loss

# <1> 构建模型，使用未经预训练的resnet18作为分类预测模型
model = _______________________
# <2> 设置模型全连接层的输入和输出维度，输入维度保持不变，输出维度定义为类别数量，最后将模型移动到和数据相同的设备上。
# 获取resnet18全连接层的输入维度
in_feats = _______________________
# 重新设置全连接层的输入和输出维度
model.fc = _______________________
# 将模型移动到和数据相同的设备上
_______________________
# <3> 选择步骤4中构造的交叉熵损失作为损失函数
criterion = _______________________
# <4> 使用torch.optim中预设的Adam优化器，仅需要设置用于迭代优化的参数params和学习率lr
optimizer = _______________________

print("修改后模型的全连接层为: ", model.fc)
print("使用的损失函数为: ", criterion)
print("使用的优化器为: ", optimizer)
 

from tqdm import tqdm
# 定义训练过程
def train(model, device, train_loader, optimizer, epoch):
    # <1> 将模型设置为训练模式
    _______________________
    # 初始化loss和总训练样本数
    sum_loss = 0
    total_num = len(train_loader.dataset)
    # 打印总训练样本数和总iteration数
    print(f'总训练样本数: {total_num}, 总iteration数: {len(train_loader)}')
    for batch_idx, (data, target) in tqdm(enumerate(train_loader)):
        data, target = Variable(data).to(device), Variable(target).to(device)
        output = model(data)
        # <2> 计算损失函数并进行梯度反传及更新
        # 计算当前batch的损失函数
        loss = _______________________
        # 将当前batch的梯度初始化为零
        optimizer._______________________
        # 反向传播求梯度
        loss._______________________
        # 更新所有参数
        optimizer._______________________
        print_loss = loss.data.item()
        sum_loss += print_loss
        # 每50个iteration打印当前的训练进度和损失函数
        if (batch_idx + 1) % 200 == 0:
            print('Train Epoch: {} [{}/{} ({:.0f}%)]\tLoss: {:.6f}'.format(
                epoch, (batch_idx + 1) * len(data), len(train_loader.dataset),
                       100. * (batch_idx + 1) / len(train_loader), loss.item()))
    # <3> 计算该epoch的平均loss
    ave_loss = _______________________
    print('epoch:{},loss:{}'.format(epoch, ave_loss))
 
# 定义测试过程
def val(model, device, test_loader):
    # <4> 将模型设置到评估模式
    _______________________
    test_loss = 0
    # 初始化准确预测样本的数量
    correct = 0
    total_num = len(test_loader.dataset)
    # 打印总测试样本数和总iteration数
    print(f'总测试样本数: {total_num}, 总iteration数: {len(test_loader)}')
    with torch.no_grad():
        for data, target in tqdm(test_loader):
            data, target = Variable(data).to(device), Variable(target).to(device)
            output = model(data)
            loss = criterion(output, target)
            _, pred = torch.max(output.data, 1)
            # <5> 判断预测结果是否准确，若准确则增加准确预测数
            _______________________
            print_loss = loss.data.item()
            test_loss += print_loss
        correct = correct.data.item()
        # <6> 计算模型准确率
        acc = _______________________
        avgloss = test_loss / len(test_loader)
        print('\nVal set: Average loss: {:.4f}, Accuracy: {}/{} ({:.0f}%)\n'.format(
            avgloss, correct, len(test_loader.dataset), 100 * acc))

# 训练
for epoch in range(1, EPOCHS + 1):
    train(model, DEVICE, train_loader, optimizer, epoch)
    val(model, DEVICE, test_loader)
# 保存模型
torch.save(model, 'model_resnet18.pth')

# 测试结果及可视化
import torch.utils.data.distributed
import torchvision.transforms as transforms
from torch.autograd import Variable
import os
from PIL import Image
import matplotlib.pyplot as plt
import cv2

# <1> 将单通道灰度图转化为三通道的彩色图像
def gray_to_rgb(gray_data):
    # 使用np.zeros初始化彩色图像rgb_data，要求图像通道数为3，且大小保持与灰度图相同
    rgb_data = np.zeros(_______________________,dtype=np.uint8)
    # 将三个通道的像素值均赋值为原本灰度图中的像素值
    _______________________ = gray_data
    _______________________ = gray_data
    _______________________ = gray_data
    
    return torch.tensor(rgb_data).float()
 
classes = ('NORMAL', 'PNEUMONIA')
# <2> 进行数据预处理，要求与测试集的预处理方式一致
transform_test_ = _______________________
# 读取保存的模型
model = torch.load("model_resnet18.pth")
# <3> 将模型设置到评估模式
_______________________
model.to(DEVICE)
path = 'data/vis/'
# 根据path读取测试样本列表
testList = os.listdir(path)[:1]

# 分类样本并可视化
fig = plt.figure()
idx= 0
# 预测并分别展示样本的分类结果
for file in testList:
    # <4> 预测待测样本的分类结果
    img_ori = Image.open(path + file)
    # 预处理读取的图像
    img = _______________________
    # 将预处理好的单通道图像转化为三通道彩色图像
    img = _______________________
    # 使用unsqueeze在第0维扩大图像维度
    img_ = _______________________
    img_ = Variable(img_).to(DEVICE)
    out = model(img_)
    # 预测分类
    _, pred = torch.max(out.data, 1)
    # <5> 使用plt设置1行4列的子图展示预测结果，通过定义的idx表示当前子图位置
    _______________________
    # 自动调整子图参数，使之填充满整个图像区域
    plt.tight_layout()
    # 展示图像及其预测类别，类别作为图标题
    plt.imshow(img_ori)
    plt.title("{}".format(classes[pred.data.item()]))
    plt.axis('off')
    # <5> 更新当前的位置标识idx
    _______________________

# 展示图象
plt.show()

# 定义恒等变换，获取全连接层的输入特征
class Identity(nn.Module):
    def __init__(self):
        super(Identity, self).__init__()

    def forward(self, x):
        return x

from tqdm import tqdm

classes = ['NORMAL', 'PNEUMONIA']
# 读取用于可视化的测试集2
path='data/test/'
# <1> 读取测试集数据并同时进行预处理，使用transform_test进行预处理
dataset_test_ = _______________________
# <1> 使用torch.utils.data.DataLoader创建测试集的Dataloader对象
# 要求设置Dataloader的dataset 、batch_size和shuffle三个参数，不重新打乱数据
test_loader_ = _______________________

model_cls = torch.load("model_resnet18.pth")
# <2> 将模型设置到评估模式
_______________________
model_cls.to(DEVICE)

cls = []
with torch.no_grad():
    for data, target in test_loader_:
        data, target = Variable(data).to(DEVICE), Variable(target).to(DEVICE)
        output = model_cls(data)
        cls.append(output)
cls = torch.cat(cls)
_, cls_pred = torch.max(cls, 1)
print(cls_pred)

model_feat = torch.load("model_resnet18.pth")
# <3> 为获取测试样本特征，使用恒等变换替换model_feat的全连接层，并将该模型设置为评估模式
_______________________
_______________________
model_feat.to(DEVICE)

feats = []
with torch.no_grad():
    for data, target in test_loader_:
        data, target = Variable(data).to(DEVICE), Variable(target).to(DEVICE)
        output = model_cls(data)
        feats.append(output)
# <4> 使用cat函数将列表feats拼接为一个二维张量
_______________________
print(feats.shape)

from sklearn.manifold import TSNE
import seaborn as sns

# <5> 根据测试集特征和预测结果绘制平面t-SNE散点图
# 定义TSNE，要求设置n_components，init和random_state三个参数
tsne = _______________________
# <6> 使用tsne将feats投影到嵌入空间，并返回转换结果
test_feats_emb = _______________________
labels = cls_pred
labels = labels.tolist()
str_labels = []
for label in labels:
    if label == 0:
        label = 'NORMAL'
    else:
        label = 'PNEUMONIA'
    str_labels.append(label)
sns.scatterplot(x=test_feats_emb[:,0], y=test_feats_emb[:,1], hue=str_labels)

