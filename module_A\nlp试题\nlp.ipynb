{"cells": [{"cell_type": "markdown", "id": "46158c2f-d281-4e06-ab3f-3f7743c31162", "metadata": {}, "source": ["**任务一说明（命名实体提取“NER”)**"]}, {"cell_type": "markdown", "id": "62a5e49c-a295-48eb-904b-420a3a211a9a", "metadata": {}, "source": ["1.数据处理（convert2bioes.py）"]}, {"cell_type": "markdown", "id": "c50debcc-d3dd-4feb-abfc-f6a0009e3896", "metadata": {}, "source": ["（1）步骤1.转换文件格式\n"]}, {"cell_type": "code", "execution_count": null, "id": "e1dd7e56-556e-4ca4-a26d-91a10ed8e23b", "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "from typing import List, Dict, Any, Tuple\n", "\n", "def split_sentences(text: str) -> List[str]:\n", "    \"\"\"\n", "    根据特定标点符号分割文本为句子列表\n", "    \n", "    Args:\n", "        text: 待分割的文本\n", "    \n", "    Returns:\n", "        分割后的句子列表\n", "    \"\"\"\n", "    sentence_endings = \"。！？\"\n", "    sentences = []\n", "    start = 0\n", "\n", "    for i in range(len(text)):\n", "        if text[i] in sentence_endings:\n", "            # <1>添加句子到sentences列表\n", "            sentences._____\n", "            # <1>更新start位置\n", "            start = _____\n", "    if start < len(text):\n", "        # <2>处理剩余文本\n", "        sentences._____\n", "\n", "    return sentences"]}, {"cell_type": "markdown", "id": "6751fedd-ad25-4ee2-be56-04afffa8792f", "metadata": {}, "source": ["（2）步骤2.根据JSON格式数据划分BIOES标注数据"]}, {"cell_type": "code", "execution_count": null, "id": "cff22b86-d3fa-4adc-bd5b-2e5d5cf5a882", "metadata": {}, "outputs": [], "source": ["def process_entity(\n", "    sentence: str, \n", "    sent_text: str, \n", "    entity: Dict[str, Any],\n", "    tags: List[str]\n", ") -> None:\n", "    \"\"\"\n", "    处理单个实体，生成对应的BIOES标签\n", "    \n", "    Args:\n", "        sentence: 当前处理的句子\n", "        sent_text: 包含实体的完整文本\n", "        entity: 实体信息字典\n", "        tags: 标签列表，将被修改\n", "        B: 实体的开始\n", "        E: 实体的结束\n", "        I: 实体的内部\n", "        S: 单字符实体\n", "        O: 非实体\n", "    \"\"\"\n", "    start_idx = entity.get('start_idx', 0) - sent_text.index(sentence)\n", "    end_idx = entity.get('end_idx', 0) - sent_text.index(sentence)\n", "    entity_type = entity.get('entity_type', '')\n", "    \n", "    # 检查实体索引是否在当前句子范围内\n", "    if start_idx < 0 or end_idx > len(sentence):\n", "        return\n", "\n", "    # 标签格式：标签类型-实体类型，如'S-PER'、'B-ORG'等\n", "    if start_idx == end_idx - 1:\n", "        # <1>单字实体使用S标签\n", "        tags[start_idx] = _____\n", "    else:\n", "        # <2>多字实体使用B-I-E标签组合\n", "        tags[start_idx] = _____\n", "        for i in range(start_idx + 1, end_idx - 1):\n", "            tags[i] = _____\n", "        tags[end_idx - 1] = _____"]}, {"cell_type": "markdown", "id": "1b652bbd-2893-4df6-9bfb-621ba7268af5", "metadata": {}, "source": ["（3）步骤3.根据特定标点符号分割文本为句子列表"]}, {"cell_type": "code", "execution_count": null, "id": "e3866998-341e-430d-a1cb-06c809cb333b", "metadata": {}, "outputs": [], "source": ["def convert_to_bioes(json_data: Dict[str, Any]) -> List[str]:\n", "    \"\"\"\n", "    将JSON格式的数据转换为BIOES标注格式\n", "    \n", "    Args:\n", "        json_data: 包含段落和实体信息的JSON数据\n", "    \n", "    Returns:\n", "        BIOES格式的行列表\n", "    \"\"\"\n", "    bioes_lines = []\n", "    \n", "    for paragraph in json_data.get('paragraphs', []):\n", "        paragraph_text = paragraph.get('paragraph', '')\n", "        sentences = split_sentences(paragraph_text)\n", "        \n", "        for sentence in sentences:\n", "            # 初始化标签列表\n", "            tags = ['O'] * len(sentence)\n", "\n", "            # 遍历段落中的句子信息，处理实体标注\n", "            for sent_info in paragraph.get('sentences', []):\n", "                sent_text = sent_info.get('sentence', '')\n", "                if sentence in sent_text:\n", "                    entities = sent_info.get('entities', [])\n", "                    for entity in entities:\n", "                        # <1>调用定义的函数处理句子中的实体标注\n", "                        _____\n", "            # 生成BIOES格式的行，忽略空格、回车和制表符\n", "            for char, tag in zip(sentence, tags):\n", "                if char.strip():\n", "                    bioes_lines.append(f'{char} {tag}')\n", "            bioes_lines.append('')  # 句子之间用空行隔开\n", "            \n", "    \n", "    return bioes_lines"]}, {"cell_type": "markdown", "id": "08496d26-af4a-47df-a764-66e9918563c1", "metadata": {}, "source": ["（4）步骤4.完成格式转变"]}, {"cell_type": "code", "execution_count": null, "id": "0301b0f9-17ee-47df-99b8-69cdff6072b1", "metadata": {}, "outputs": [], "source": ["def process_directory(input_dir: str) -> List[str]:\n", "    \"\"\"\n", "    处理目录中的所有JSON文件，转换为BIOES格式\n", "    \n", "    Args:\n", "        input_dir: 输入目录路径\n", "    \n", "    Returns:\n", "        所有文件的BIOES格式行列表\n", "    \"\"\"\n", "    all_bioes_lines = []\n", "    \n", "    # 遍历目录中的所有JSON文件\n", "    for filename in os.listdir(input_dir):\n", "        if filename.endswith('.json'):\n", "            file_path = os.path.join(input_dir, filename)\n", "            try:\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    # <1>加载JSON数据\n", "                    json_data = json._____\n", "                    # <2>转换为BIOES格式 \n", "                    bioes_lines = _____\n", "                    # <2>添加到总列表进行合并\n", "                    all_bioes_lines._____\n", "            except Exception as e:\n", "                print(f\"处理文件 {filename} 时出错: {e}\")\n", "    \n", "    return all_bioes_lines"]}, {"cell_type": "markdown", "id": "86dc5b98-b40d-4452-8efd-2377280e664e", "metadata": {}, "source": ["2.命名实体识别"]}, {"cell_type": "markdown", "id": "ea6d10b9-91ce-42dd-872d-cb24219ad94b", "metadata": {}, "source": ["（1）步骤1.构建模型输入数据(data.py)"]}, {"cell_type": "code", "execution_count": null, "id": "2d697751-302c-4809-8c9d-09a505876258", "metadata": {}, "outputs": [], "source": ["from os.path import join\n", "from codecs import open\n", "\n", "\n", "def build_map(lists):\n", "    maps = {}\n", "    for list_ in lists:\n", "        for e in list_:\n", "            if e not in maps:\n", "                maps[e] = len(maps)\n", "    return maps\n", "\n", "def build_corpus(split, make_vocab=True, data_dir=\"./data\"):\n", "    \"\"\"读取数据\"\"\"\n", "    assert split in ['train', 'dev', 'test']\n", "\n", "    word_lists = []\n", "    tag_lists = []\n", "    with open(join(data_dir, split+\".bioes\"), 'r', encoding='utf-8') as f:\n", "        word_list = []\n", "        tag_list = []\n", "        # 读取BIOES格式文件并解析词和标签\n", "        for line in f:\n", "            if line != '\\n':\n", "                # <1>分割每行的词和标签\n", "                word, tag = _____\n", "                word_list.append(word)\n", "                tag_list.append(tag)\n", "            elif (word_list!= []) and (tag_list!= [] ):\n", "                # <1>添加到列表\n", "                word_lists._____\n", "                # <1>处理句子边界\n", "                tag_lists._____\n", "                word_list = []\n", "                tag_list = []\n", "\n", "    # 构建词汇表和标签映射\n", "    # 如果make_vocab为True，还需要返回word2id和tag2id\n", "    if make_vocab:\n", "        # <2>构建word2id映射\n", "        word2id = _____\n", "        # <2>构建tag2id映射\n", "        tag2id = _____\n", "        return word_lists, tag_lists, word2id, tag2id\n", "    else:\n", "        return word_lists, tag_lists"]}, {"cell_type": "markdown", "id": "abdcb087-ef82-433c-a9e0-f87079891443", "metadata": {}, "source": ["（2）步骤2.基于条件随机场（CRF）的序列标注模型(/models/crf.py)"]}, {"cell_type": "code", "execution_count": null, "id": "35c4b838-ea84-455b-b973-5614c549baab", "metadata": {}, "outputs": [], "source": ["from sklearn_crfsuite import CRF\n", "from .util import sent2features\n", "\n", "class CRFModel(object):\n", "    def __init__(self,\n", "                 algorithm='lbfgs',\n", "                 c1=0.1,\n", "                 c2=0.1,\n", "                 max_iterations=100,\n", "                 all_possible_transitions=False\n", "                 ):\n", "\n", "        self.model = CRF(algorithm=algorithm,\n", "                         c1=c1,\n", "                         c2=c2,\n", "                         max_iterations=max_iterations,\n", "                         all_possible_transitions=all_possible_transitions)\n", "\n", "    def train(self, sentences, tag_lists):\n", "        # <1>提取句子特征\n", "        features = _____\n", "        # <2>训练模型\n", "        self.model._____\n", "\n", "    def test(self, sentences):\n", "        # <1>提取句子特征 \n", "        features = _____\n", "        # <2>预测标签\n", "        pred_tag_lists = self.model._____\n", "        return pred_tag_lists"]}, {"cell_type": "markdown", "id": "12bd8c21-c706-41f8-b3eb-97b7ecb6c2ba", "metadata": {}, "source": ["（3）步骤3.基于双向LSTM（BiLSTM）的序列标注模型(/models/bilstm.py)"]}, {"cell_type": "code", "execution_count": null, "id": "ccd5d1db-e45d-4a09-9a42-e5708e5ed923", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "from torch.nn.utils.rnn import pad_packed_sequence, pack_padded_sequence\n", "\n", "\n", "class BiLSTM(nn.Module):\n", "    def __init__(self, vocab_size, emb_size, hidden_size, out_size):\n", "        \"\"\"初始化参数：\n", "            vocab_size:字典的大小\n", "            emb_size:词向量的维数\n", "            hidden_size：隐向量的维数\n", "            out_size:标注的种类\n", "        \"\"\"\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        # <1>定义词向量层，包括词典大小、词向量维度\n", "        self.embedding = _____\n", "        # <2>定义BiLSTM层，包括输入维度、隐藏层维度，再设置batch_first=True、bidirectional=True\n", "        self.bilstm = nn.LSTM(\n", "            _____\n", "            )\n", "        # <3>定义输出全连接层，输入维度（BiLSTM隐藏层维度*2）、输出维度（标签数量）\n", "        self.lin = nn.Linear(\n", "            _____\n", "        )\n", "\n", "    def forward(self, sents_tensor, lengths):\n", "        # 实现BiLSTM前向传播\n", "        # <4>词向量化\n", "        emb = _____\n", "\n", "        packed = pack_padded_sequence(emb, lengths, batch_first=True)\n", "        # <4>BiLSTM编码\n", "        rnn_out, _ = _____\n", "        rnn_out, _ = pad_packed_sequence(rnn_out, batch_first=True)\n", "\n", "        # <4>全连接层输出\n", "        scores = _____\n", "        return scores\n", "\n", "    def test(self, sents_tensor, lengths, _):\n", "        \"\"\"第三个参数不会用到，加它是为了与BiLSTM_CRF保持同样的接口\"\"\"\n", "        logits = self.forward(sents_tensor, lengths)  # [B, L, out_size]\n", "        _, batch_tagids = torch.max(logits, dim=2)\n", "\n", "        return batch_tagids"]}, {"cell_type": "markdown", "id": "65e975d6-a084-4d69-b0ef-07f03cb3755d", "metadata": {}, "source": ["（4）步骤4.基于隐马尔可夫模型（HMM）的序列标注模型(/models/hmm.py)"]}, {"cell_type": "code", "execution_count": null, "id": "c98d7052-8929-4b41-bc8c-e2ca8515bd18", "metadata": {}, "outputs": [], "source": ["import torch\n", "\n", "\n", "class HMM(object):\n", "    def __init__(self, N, M):\n", "        \"\"\"Args:\n", "            N: 状态数，这里对应存在的标注的种类\n", "            M: 观测数，这里对应有多少不同的字\n", "        \"\"\"\n", "        self.N = N\n", "        self.M = M\n", "\n", "        # 状态转移概率矩阵 A[i][j]表示从i状态转移到j状态的概率\n", "        self.A = torch.zeros(N, N)\n", "        # 观测概率矩阵, B[i][j]表示i状态下生成j观测的概率\n", "        self.B = torch.zeros(N, M)\n", "        # 初始状态概率  Pi[i]表示初始时刻为状态i的概率\n", "        self.Pi = torch.zeros(N)\n", "\n", "    def train(self, word_lists, tag_lists, word2id, tag2id):\n", "        \"\"\"HMM的训练，即根据训练语料对模型参数进行估计,\n", "           因为我们有观测序列以及其对应的状态序列，所以我们\n", "           可以使用极大似然估计的方法来估计隐马尔可夫模型的参数\n", "        参数:\n", "            word_lists: 列表，其中每个元素由字组成的列表，如 ['担','任','科','员']\n", "            tag_lists: 列表，其中每个元素是由对应的标注组成的列表，如 ['O','O','B-TITLE', 'E-TITLE']\n", "            word2id: 将字映射为ID\n", "            tag2id: 字典，将标注映射为ID\n", "        \"\"\"\n", "\n", "        assert len(tag_lists) == len(word_lists)\n", "\n", "        # 估计状态转移概率矩阵\n", "        for tag_list in tag_lists:\n", "            seq_len = len(tag_list)\n", "            for i in range(seq_len - 1):\n", "                # <1>获取当前和下一个标签ID\n", "                current_tagid = _____\n", "                next_tagid = _____\n", "                # <1>更新转移计数\n", "                self.A[current_tagid][next_tagid] += _____\n", "        # 如果某元素没有出现过，该位置为0，这在后续的计算中是不允许的,因此我们将等于0的概率加上很小的数\n", "        self.A[self.A == 0.] = 1e-10\n", "        # <1>归一化\n", "        self.A = _____\n", "\n", "        # 估计观测概率矩阵\n", "        for tag_list, word_list in zip(tag_lists, word_lists):\n", "            assert len(tag_list) == len(word_list)\n", "            for tag, word in zip(tag_list, word_list):\n", "                # <2>获取标签和词的ID\n", "                tag_id = _____\n", "                word_id = _____\n", "                # <2>更新观测计数\n", "                self.B[tag_id][word_id] += _____\n", "        self.B[self.B == 0.] = 1e-10\n", "        # <2>归一化\n", "        self.B = _____\n", "\n", "        # 估计初始状态概率分布\n", "        for tag_list in tag_lists:\n", "            # <3>获取序列第一个标签ID\n", "            init_tagid = _____\n", "            # <3>更新初始状态计数\n", "            self.Pi[init_tagid] += _____\n", "        self.Pi[self.Pi == 0.] = 1e-10\n", "        # <3>归一化\n", "        self.Pi = _____\n", "\n", "    def test(self, word_lists, word2id, tag2id):\n", "        pred_tag_lists = []\n", "        for word_list in word_lists:\n", "            pred_tag_list = self.decoding(word_list, word2id, tag2id)\n", "            pred_tag_lists.append(pred_tag_list)\n", "        return pred_tag_lists\n", "\n", "    def decoding(self, word_list, word2id, tag2id):\n", "        \"\"\"\n", "        使用维特比算法对给定观测序列求状态序列， 这里就是对字组成的序列,求其对应的标注。\n", "        维特比算法实际是用动态规划解隐马尔可夫模型预测问题，即用动态规划求概率最大路径（最优路径）\n", "        这时一条路径对应着一个状态序列\n", "        \"\"\"\n", "        # 整条链很长的情况下，十分多的小概率相乘，最后可能造成下溢\n", "        # 采用对数概率，这样源空间中的很小概率，就被映射到对数空间的大的负数\n", "        # 同时相乘操作也变成简单的相加操作\n", "        A = torch.log(self.A)\n", "        B = torch.log(self.B)\n", "        Pi = torch.log(self.Pi)\n", "\n", "        # 初始化 维比特矩阵viterbi 它的维度为[状态数, 序列长度]\n", "        # 其中viterbi[i, j]表示标注序列的第j个标注为i的所有单个序列(i_1, i_2, ..i_j)出现的概率最大值\n", "        seq_len = len(word_list)\n", "        viterbi = torch.zeros(self.N, seq_len)\n", "        # backpointer是跟viterbi一样大小的矩阵\n", "        # backpointer[i, j]存储的是 标注序列的第j个标注为i时，第j-1个标注的id\n", "        # 等解码的时候，我们用backpointer进行回溯，以求出最优路径\n", "        backpointer = torch.zeros(self.N, seq_len).long()\n", "\n", "        # self.Pi[i] 表示第一个字的标记为i的概率\n", "        # Bt[word_id]表示字为word_id的时候，对应各个标记的概率\n", "        # self.A.t()[tag_id]表示各个状态转移到tag_id对应的概率\n", "\n", "        # 所以第一步为\n", "        start_wordid = word2id.get(word_list[0], None)\n", "        Bt = B.t()\n", "        if start_wordid is None:\n", "            # 如果字不在字典里，则假设状态的概率分布是均匀的\n", "            bt = torch.log(torch.ones(self.N) / self.N)\n", "        else:\n", "            bt = Bt[start_wordid]\n", "        viterbi[:, 0] = Pi + bt\n", "        backpointer[:, 0] = -1\n", "\n", "        # 递推公式：\n", "        # viterbi[tag_id, step] = max(viterbi[:, step-1]* self.A.t()[tag_id] * Bt[word])\n", "        # 其中word是step时刻对应的字\n", "        # 由上述递推公式求后续各步\n", "        for step in range(1, seq_len):\n", "            wordid = word2id.get(word_list[step], None)\n", "            # 处理字不在字典中的情况\n", "            # bt是在t时刻字为wordid时，状态的概率分布\n", "            if wordid is None:\n", "                # 如果字不在字典里，则假设状态的概率分布是均匀的\n", "                bt = torch.log(torch.ones(self.N) / self.N)\n", "            else:\n", "                bt = Bt[wordid]  # 否则从观测概率矩阵中取bt\n", "            for tag_id in range(len(tag2id)):\n", "                # 实现维特比算法递推公式\n", "                # <4>计算前一步概率+转移概率+观测概率\n", "                max_prob, max_id = torch.max(\n", "                    _____\n", "                )\n", "                # <4>找到最大值和对应状态     \n", "                viterbi[tag_id, step] = _____\n", "                backpointer[tag_id, step] = max_id\n", "\n", "        # 终止， t=seq_len 即 viterbi[:, seq_len]中的最大概率，就是最优路径的概率\n", "        # 找到最优路径的终点状态\n", "        # <5>从最后一个时间步的所有状态中找到概率最大的状态\n", "        best_path_prob, best_path_pointer = torch.max(\n", "            _____\n", "        )\n", "\n", "        # 回溯，求最优路径\n", "        best_path_pointer = best_path_pointer.item()\n", "        best_path = [best_path_pointer]\n", "        \n", "        # <6>从终点开始，利用backpointer逐步回溯到起点\n", "        for back_step in range(seq_len-1, 0, -1):\n", "            best_path_pointer = _____\n", "            best_path_pointer = best_path_pointer.item()\n", "            best_path.append(best_path_pointer)\n", "        \n", "        # 将tag_id组成的序列转化为tag\n", "        assert len(best_path) == len(word_list)\n", "        id2tag = dict((id_, tag) for tag, id_ in tag2id.items())\n", "        tag_list = [id2tag[id_] for id_ in reversed(best_path)]\n", "\n", "        return tag_list"]}, {"cell_type": "markdown", "id": "977d9def-9826-45c9-94f4-90f2be7b4a02", "metadata": {}, "source": ["（5）步骤5.基于双向LSTM和条件随机场（BiLSTM-CRF）的序列标注模型(/models/bilstm_crf.py)"]}, {"cell_type": "code", "execution_count": null, "id": "da89ed64-5993-4432-a771-1d541a881b24", "metadata": {}, "outputs": [], "source": ["from itertools import zip_longest\n", "from copy import deepcopy\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "\n", "from .util import tensorized, sort_by_lengths, cal_loss, cal_lstm_crf_loss\n", "from .config import TrainingConfig, LSTMConfig\n", "from .bilstm import BiLSTM\n", "\n", "\n", "class BiLSTM_CRF(nn.Module):\n", "    def __init__(self, vocab_size, emb_size, hidden_size, out_size):\n", "        \"\"\"初始化参数：\n", "            vocab_size:字典的大小\n", "            emb_size:词向量的维数\n", "            hidden_size：隐向量的维数\n", "            out_size:标注的种类\n", "        \"\"\"\n", "        super(BiLSTM_CRF, self).__init__()\n", "        self.bilstm = BiLSTM(vocab_size, emb_size, hidden_size, out_size)\n", "\n", "        # CRF实际上就是多学习一个转移矩阵 [out_size, out_size] 并进行均匀初始化\n", "        # 定义CRF转移矩阵\n", "        # <1>创建可训练的转移矩阵参数，大小为(out_size, out_size)\n", "        self.transition = nn.Parameter(\n", "            _____\n", "            )\n", "        # self.transition.data.zero_()\n", "\n", "    def forward(self, sents_tensor, lengths):\n", "        # [B, L, out_size]\n", "        # 计算BiLSTM-CRF的前向传播\n", "        # <2>获取BiLSTM发射分数\n", "        emission = _____\n", "        \n", "        # 计算CRF scores, 这个scores大小为[B, L, out_size, out_size]\n", "        # 也就是每个字对应对应一个 [out_size, out_size]的矩阵\n", "        # 这个矩阵第i行第j列的元素的含义是：上一时刻tag为i，这一时刻tag为j的分数\n", "        batch_size, max_len, out_size = emission.size()\n", "        # <2>计算CRF分数矩阵\n", "        crf_scores = _____\n", "        return crf_scores\n", "\n", "    def test(self, test_sents_tensor, lengths, tag2id):\n", "        \"\"\"使用维特比算法进行解码\"\"\"\n", "        start_id = tag2id['<start>']\n", "        end_id = tag2id['<end>']\n", "        pad = tag2id['<pad>']\n", "        tagset_size = len(tag2id)\n", "\n", "        crf_scores = self.forward(test_sents_tensor, lengths)\n", "        device = crf_scores.device\n", "        # B:batch_size, L:max_len, T:target set size\n", "        B, L, T, _ = crf_scores.size()\n", "        # viterbi[i, j, k]表示第i个句子，第j个字对应第k个标记的最大分数\n", "        viterbi = torch.zeros(B, L, T).to(device)\n", "        # backpointer[i, j, k]表示第i个句子，第j个字对应第k个标记时前一个标记的id，用于回溯\n", "        backpointer = (torch.zeros(B, L, T).long() * end_id).to(device)\n", "        lengths = torch.LongTensor(lengths).to(device)\n", "        # 向前递推\n", "        for step in range(L):\n", "            batch_size_t = (lengths > step).sum().item()\n", "            \n", "            if step == 0:\n", "                # 第一个字它的前一个标记只能是start_id\n", "                viterbi[:batch_size_t, step,:] = crf_scores[: batch_size_t, step, start_id, :]\n", "                backpointer[: batch_size_t, step, :] = start_id\n", "            else:\n", "                # 实现BiLSTM-CRF的维特比解码前向递推\n", "                # <3>计算前一步概率加上当前步转移分数，找到最大值和对应的前驱标签\n", "                max_scores, prev_tags = torch.max(\n", "                    _____\n", "                )\n", "                viterbi[:batch_size_t, step, :] = max_scores\n", "                backpointer[:batch_size_t, step, :] = prev_tags\n", "        # 在回溯的时候我们只需要用到backpointer矩阵\n", "        backpointer = backpointer.view(B, -1)  # [B, L * T]\n", "        tagids = []  # 存放结果\n", "        tags_t = None\n", "        for step in range(L-1, 0, -1):\n", "            batch_size_t = (lengths > step).sum().item()\n", "            if step == L-1:\n", "                index = torch.ones(batch_size_t).long() * (step * tagset_size)\n", "                index = index.to(device)\n", "                index += end_id\n", "            else:\n", "                prev_batch_size_t = len(tags_t)\n", "\n", "                new_in_batch = torch.LongTensor(\n", "                    [end_id] * (batch_size_t - prev_batch_size_t)).to(device)\n", "                offset = torch.cat(\n", "                    [tags_t, new_in_batch],\n", "                    dim=0\n", "                )  # 这个offset实际上就是前一时刻的\n", "                index = torch.ones(batch_size_t).long() * (step * tagset_size)\n", "                index = index.to(device)\n", "                index += offset.long()\n", "\n", "            try:\n", "                tags_t = backpointer[:batch_size_t].gather(\n", "                    dim=1, index=index.unsqueeze(1).long())\n", "            except RuntimeError:\n", "                import pdb\n", "                pdb.set_trace()\n", "            tags_t = tags_t.squeeze(1)\n", "            tagids.append(tags_t.tolist())\n", "\n", "        # tagids:[L-1]（L-1是因为扣去了end_token),大小的liebiao\n", "        # 其中列表内的元素是该batch在该时刻的标记\n", "        # 下面修正其顺序，并将维度转换为 [B, L]\n", "        tagids = list(zip_longest(*reversed(tagids), fillvalue=pad))\n", "        tagids = torch.Tensor(tagids).long()\n", "\n", "        # 返回解码的结果\n", "        return tagids\n", "\n", "\n", "\n", "class BILSTM_Model(object):\n", "    def __init__(self, vocab_size, out_size, crf=True):\n", "        \"\"\"功能：对LSTM的模型进行训练与测试\n", "           参数:\n", "            vocab_size:词典大小\n", "            out_size:标注种类\n", "            crf选择是否添加CRF层\"\"\"\n", "        self.device = torch.device(\n", "            \"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "        # 加载模型参数\n", "        self.emb_size = LSTMConfig.emb_size\n", "        self.hidden_size = LSTMConfig.hidden_size\n", "\n", "        self.crf = crf\n", "        # 根据是否添加crf初始化不同的模型 选择不一样的损失计算函数\n", "        if not crf:\n", "            self.model = BiLSTM(vocab_size, self.emb_size,\n", "                                self.hidden_size, out_size).to(self.device)\n", "            self.cal_loss_func = cal_loss\n", "        else:\n", "            # 创建BiLSTM-CRF模型实例\n", "            # <4>传入词典大小、词向量维度、隐藏层维度、输出维度参数\n", "            self.model = BiLSTM_CRF(\n", "                _____\n", "            ).to(self.device)\n", "            self.cal_loss_func = cal_lstm_crf_loss\n", "\n", "        # 加载训练参数：\n", "        self.epoches = TrainingConfig.epoches\n", "        self.print_step = TrainingConfig.print_step\n", "        self.lr = TrainingConfig.lr\n", "        self.batch_size = TrainingConfig.batch_size\n", "\n", "        # 初始化优化器\n", "        # <5>使用Adam优化器，传入模型参数和学习率\n", "        self.optimizer = _____\n", "\n", "        # 初始化其他指标\n", "        self.step = 0\n", "        self._best_val_loss = 1e18\n", "        self.best_model = None\n", "\n", "    def train(self, word_lists, tag_lists,\n", "              dev_word_lists, dev_tag_lists,\n", "              word2id, tag2id):\n", "        # 对数据集按照长度进行排序\n", "        word_lists, tag_lists, _ = sort_by_lengths(word_lists, tag_lists)\n", "        dev_word_lists, dev_tag_lists, _ = sort_by_lengths(\n", "            dev_word_lists, dev_tag_lists)\n", "\n", "        B = self.batch_size\n", "        for e in range(1, self.epoches+1):\n", "            self.step = 0\n", "            losses = 0.\n", "            for ind in range(0, len(word_lists), B):\n", "                batch_sents = word_lists[ind:ind+B]\n", "                batch_tags = tag_lists[ind:ind+B]\n", "\n", "                losses += self.train_step(batch_sents,\n", "                                          batch_tags, word2id, tag2id)\n", "\n", "                if self.step % TrainingConfig.print_step == 0:\n", "                    total_step = (len(word_lists) // B + 1)\n", "                    print(\"Epoch {}, step/total_step: {}/{} {:.2f}% Loss:{:.4f}\".format(\n", "                        e, self.step, total_step,\n", "                        100. * self.step / total_step,\n", "                        losses / self.print_step\n", "                    ))\n", "                    losses = 0.\n", "\n", "            # 每轮结束测试在验证集上的性能，保存最好的一个\n", "            val_loss = self.validate(\n", "                dev_word_lists, dev_tag_lists, word2id, tag2id)\n", "            print(\"Epoch {}, Val Loss:{:.4f}\".format(e, val_loss))\n", "\n", "    def train_step(self, batch_sents, batch_tags, word2id, tag2id):\n", "        self.model.train()\n", "        self.step += 1\n", "        # 准备数据\n", "        tensorized_sents, lengths = tensorized(batch_sents, word2id)\n", "        tensorized_sents = tensorized_sents.to(self.device)\n", "        targets, lengths = tensorized(batch_tags, tag2id)\n", "        targets = targets.to(self.device)\n", "\n", "        # 完成模型前向传播和损失计算\n", "        # <6>模型前向传播获得分数\n", "        scores = _____\n", "\n", "        # 计算损失 更新参数\n", "        self.optimizer.zero_grad()\n", "        # <6>计算损失\n", "        loss = _____\n", "        loss.backward()\n", "        self.optimizer.step()\n", "\n", "        return loss.item()"]}, {"cell_type": "markdown", "id": "07c5a2f6-3cc6-4261-b957-7a5b0f40b2ec", "metadata": {}, "source": ["（6）步骤6.模型性能指标定义(evaluating.py)"]}, {"cell_type": "code", "execution_count": null, "id": "adfabb9a-b4ec-4927-a5d4-3aca7b1382dc", "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "from utils import flatten_lists\n", "\n", "\n", "class Metrics(object):\n", "    \"\"\"用于评价模型，计算每个标签的精确率，召回率，F1分数\"\"\"\n", "\n", "    def __init__(self, golden_tags, predict_tags, remove_O=False):\n", "\n", "        # [[t1, t2], [t3, t4]...] --> [t1, t2, t3, t4...]\n", "        self.golden_tags = flatten_lists(golden_tags)\n", "        self.predict_tags = flatten_lists(predict_tags)\n", "\n", "        if remove_O:  # 将O标记移除，只关心实体标记\n", "            self._remove_Otags()\n", "\n", "        # 辅助计算的变量\n", "        self.tagset = set(self.golden_tags)\n", "        self.correct_tags_number = self.count_correct_tags()\n", "        self.predict_tags_counter = Counter(self.predict_tags)\n", "        self.golden_tags_counter = Counter(self.golden_tags)\n", "\n", "        # 计算精确率\n", "        self.precision_scores = self.cal_precision()\n", "\n", "        # 计算召回率\n", "        self.recall_scores = self.cal_recall()\n", "\n", "        # 计算F1分数\n", "        self.f1_scores = self.cal_f1()\n", "\n", "    def cal_precision(self):\n", "        \"\"\"计算精确率\"\"\"\n", "        precision_scores = {}\n", "\n", "        # <1>计算精确率:正确预测的标签数量 / 预测的标签总数量\n", "        for tag in self.tagset:\n", "            # 检查预测标签数量是否为0，避免除以零\n", "            if self.predict_tags_counter[tag] == 0:\n", "                precision_scores[tag] = 0.0\n", "            else:\n", "                precision_scores[tag] = _____\n", "        return precision_scores\n", "\n", "    def cal_recall(self):\n", "        \"\"\"计算召回率\"\"\"\n", "        recall_scores = {}\n", "        # <2>计算召回率:正确预测的标签数量 / 真实标签总数量\n", "        for tag in self.tagset:\n", "            # 检查真实标签数量是否为0，避免除以零\n", "            if self.golden_tags_counter[tag] == 0:\n", "                recall_scores[tag] = 0.0\n", "            else:\n", "                recall_scores[tag] = _____           \n", "        return recall_scores\n", "\n", "    def cal_f1(self):\n", "        \"\"\"计算F1分数\"\"\"\n", "        f1_scores = {}\n", "        # <3>计算F1分数：精确率 * 召回率 / (精确率 + 召回率)\n", "        for tag in self.tagset:\n", "            p, r = self.precision_scores[tag], self.recall_scores[tag]\n", "            # 只有当精确率和召回率都为0时，F1才为0\n", "            if p == 0 and r == 0:\n", "                f1_scores[tag] = 0.0\n", "            else:\n", "                f1_scores[tag] = _____\n", "        return f1_scores"]}, {"cell_type": "markdown", "id": "efb36c4e-5f4f-42b8-9487-0965ef07f097", "metadata": {}, "source": ["（7）步骤7.模型训练和测试（evaluate.py）"]}, {"cell_type": "code", "execution_count": null, "id": "e1782ffc-d2d6-422f-bc4b-6862fcb18857", "metadata": {}, "outputs": [], "source": ["import time\n", "from collections import Counter\n", "\n", "from models.hmm import HMM\n", "from models.crf import CRFModel\n", "from models.bilstm_crf import BILSTM_Model\n", "from utils import save_model, flatten_lists\n", "from evaluating import Metrics\n", "\n", "\n", "def crf_train_eval(train_data, test_data, remove_O=False):\n", "\n", "    # 训练CRF模型\n", "    train_word_lists, train_tag_lists = train_data\n", "    test_word_lists, test_tag_lists = test_data\n", "    \n", "    # CRF模型训练、保存和测试\n", "    crf_model = CRFModel()\n", "    # <1>训练模型 \n", "    crf_model._____\n", "    # <1>保存模型\n", "    save_model(\n", "        _____\n", "    )\n", "    \n", "    # <1>测试模型\n", "    pred_tag_lists = crf_model._____\n", "\n", "    metrics = Metrics(test_tag_lists, pred_tag_lists, remove_O=remove_O)\n", "    #------------------------------------------------------1---------------------------------------------\n", "    \n", "    metrics.report_scores()\n", "    metrics.report_confusion_matrix()\n", "\n", "    return pred_tag_lists\n", "\n", "\n", "def hmm_train_eval(train_data, test_data, word2id, tag2id, remove_O=False):\n", "    \"\"\"训练并评估hmm模型\"\"\"\n", "    # 训练HMM模型\n", "    train_word_lists, train_tag_lists = train_data\n", "    test_word_lists, test_tag_lists = test_data\n", "\n", "    # HMM模型训练、保存和测试\n", "    hmm_model = HMM(len(tag2id), len(word2id))\n", "    # <2>训练模型\n", "    hmm_model._____\n", "    # <2>保存模型\n", "    save_model(\n", "        _____\n", "    )\n", "\n", "    # <2>测试模型\n", "    pred_tag_lists = hmm_model._____\n", "\n", "    metrics = Metrics(test_tag_lists, pred_tag_lists, remove_O=remove_O)\n", "        \n", "    metrics.report_scores()\n", "    metrics.report_confusion_matrix()\n", "\n", "    return pred_tag_lists\n", "\n", "\n", "def bilstm_train_and_eval(train_data, dev_data, test_data,\n", "                          word2id, tag2id, crf=True, remove_O=False):\n", "    train_word_lists, train_tag_lists = train_data\n", "    dev_word_lists, dev_tag_lists = dev_data\n", "    test_word_lists, test_tag_lists = test_data\n", "\n", "    start = time.time()\n", "    vocab_size = len(word2id)\n", "    out_size = len(tag2id)\n", "    # BiLSTM模型训练、保存和测试\n", "    bilstm_model = BILSTM_Model(vocab_size, out_size, crf=crf)\n", "    # <3>训练模型\n", "    bilstm_model._____\n", "    model_name = \"bilstm_crf\" if crf else \"bilstm\"\n", "    # <3>保存模型\n", "    save_model(\n", "        \n", "    )\n", "\n", "    print(\"训练完毕,共用时{}秒.\".format(int(time.time()-start)))\n", "    print(\"评估{}模型中...\".format(model_name))\n", "    # <3>测试模型\n", "    pred_tag_lists, test_tag_lists = bilstm_model._____\n", "\n", "    metrics = Metrics(test_tag_lists, pred_tag_lists, remove_O=remove_O)\n", "    \n", "    metrics.report_scores()\n", "    metrics.report_confusion_matrix()\n", "\n", "    return pred_tag_lists"]}, {"cell_type": "markdown", "id": "78fb1afc-ec74-4da0-ac46-bd334ee19579", "metadata": {}, "source": ["**任务二说明（三元组提取“KGC”）**"]}, {"cell_type": "markdown", "id": "f4eee433-ba5a-4f5e-9c37-f3357e4e4935", "metadata": {}, "source": ["1.数据处理（data/raw_data_process.py）"]}, {"cell_type": "code", "execution_count": null, "id": "feb60bf6-eeea-4e44-8a9f-3d9f8997f4bf", "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "\n", "\n", "# 1.数据处理\n", "def jsonl_to_txt(jsonl_file_path, text_output_path, triple_output_path):\n", "    \"\"\"\n", "    将JSONL文件转换为两个TXT文件：\n", "    - text_output_path: 每行存储\"text\"字段内容\n", "    - triple_output_path: 每行存储\"triple_list\"的JSON字符串\n", "    \"\"\"\n", "    try:\n", "        with open(jsonl_file_path, 'r', encoding='utf-8') as jsonl_file, \\\n", "                open(text_output_path, 'w', encoding='utf-8') as text_file, \\\n", "                open(triple_output_path, 'w', encoding='utf-8') as triple_file:\n", "\n", "            line_count = 0\n", "            for line in jsonl_file:\n", "                # <1>解析JSON数据\n", "                data = json._____\n", "\n", "                # <2>提取text字段\n", "                text_content = data._____\n", "                text_file.write(text_content + \"\\n\")\n", "\n", "                # <3>提取triple_list字段并序列化为JSON字符串\n", "                triple_list = data._____\n", "                triple_file.write(json.dumps(triple_list, ensure_ascii=False) + \"\\n\")\n", "                line_count += 1\n", "                if line_count % 1000 == 0:  # 每处理1000行打印进度\n", "                    print(f\"已处理 {line_count} 行...\")\n", "\n", "            print(f\"转换完成！共处理 {line_count} 行数据\")\n", "            print(f\"文本文件保存至: {os.path.abspath(text_output_path)}\")\n", "            print(f\"三元组文件保存至: {os.path.abspath(triple_output_path)}\")\n", "\n", "    except FileNotFoundError:\n", "        print(f\"错误: 文件 {jsonl_file_path} 不存在\")\n", "    except json.JSONDecodeError as e:\n", "        print(f\"JSON解析错误: {e}\")\n", "    except Exception as e:\n", "        print(f\"未知错误: {e}\")\n", "\n", "# 示例用法\n", "if __name__ == \"__main__\":\n", "    jsonl_to_txt(\n", "        jsonl_file_path=\"CMeIE-V2_test_triples.jsonl\",  # 输入的JSONL文件路径\n", "        text_output_path=\"source.txt\",  # 输出的文本文件路径\n", "        triple_output_path=\"target.txt\"  # 输出的三元组文件路径\n", "    )"]}, {"cell_type": "markdown", "id": "a3ab8394-5840-486a-9e30-269dcc08084f", "metadata": {}, "source": ["2.知识图谱构建（base/use_llm.py）"]}, {"cell_type": "markdown", "id": "ac09beeb-c0ca-4b6a-85e0-07ecd0891e0b", "metadata": {}, "source": ["（1）步骤1.调用大模型提取三元组"]}, {"cell_type": "code", "execution_count": null, "id": "3c4a083a-7498-4519-8ced-15ca99672719", "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python3\n", "# -*- coding: utf-8 -*-\n", "\"\"\"\n", "运行编译后的use_llm.pyc文件的脚本\n", "支持传入text_path、result_path、few_shot和prompt四个参数\n", "\"\"\"\n", "\n", "import sys\n", "import os\n", "import ast\n", "import importlib.util\n", "\n", "def load_pyc_module(pyc_path):\n", "    \"\"\"加载pyc文件作为模块\"\"\"\n", "    spec = importlib.util.spec_from_file_location(\"use_llm_module\", pyc_path)\n", "    module = importlib.util.module_from_spec(spec)\n", "    spec.loader.exec_module(module)\n", "    return module\n", "\n", "def main():\n", "    # <1>添加text_path（源文件路径）、result_path（保存文件路径）参数\n", "    text_path = _____\n", "    result_path = _____\n", "\n", "    # 检查输入文件是否存在\n", "    if not os.path.exists(text_path):\n", "        print(f\"错误：输入文件不存在: {text_path}\")\n", "        sys.exit(1)\n", "\n", "    # 创建输出目录（如果不存在）\n", "    result_dir = os.path.dirname(result_path)\n", "    if result_dir and not os.path.exists(result_dir):\n", "        os.makedirs(result_dir)\n", "\n", "    # <2>设置few_shot示例\n", "    few_shot = {\n", "        \"user input text\": _____,\n", "        \"output triples\": _____\n", "    }\n", "\n", "    # <3>设置prompt提示词\n", "    prompt = (_____)\n", "\n", "    # 查找pyc文件\n", "    pyc_path = os.path.join(os.path.dirname(__file__), \"use_llm.pyc\")\n", "    if not os.path.exists(pyc_path):\n", "        print(f\"错误：找不到编译文件: {pyc_path}\")\n", "        sys.exit(1)\n", "\n", "    try:\n", "        # 加载pyc模块\n", "        print(\"加载编译模块...\")\n", "        llm_module = load_pyc_module(pyc_path)\n", "        \n", "        # 调用主函数\n", "        print(f\"开始处理文件: {text_path}\")\n", "        print(f\"输出路径: {result_path}\")\n", "        \n", "        llm_module.main(text_path, result_path, few_shot, prompt)\n", "        \n", "        print(\"处理完成！\")\n", "        \n", "    except Exception as e:\n", "        print(f\"执行错误: {e}\")\n", "        sys.exit(1)\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "id": "5ba720aa-4db6-4a6a-a0a0-1c6e2a40b8de", "metadata": {}, "source": ["3.精度评测（base/evaluation_script.py）"]}, {"cell_type": "markdown", "id": "b2f13862-13dd-4580-88a2-9595905a5897", "metadata": {}, "source": ["（2）步骤1. 衡量提取精度"]}, {"cell_type": "code", "execution_count": null, "id": "943a7951-966c-47e8-83ce-306635f39a1c", "metadata": {}, "outputs": [], "source": ["# 主计算逻辑\n", "def main(result_path, gold_path):\n", "    # 解析两个文件\n", "    pred_data = parse_file(result_path)\n", "    true_data = parse_file(gold_path)\n", "    \n", "    # 确保pred_data长度与true_data一致（不足补空列表）\n", "    if len(pred_data) < len(true_data):\n", "        pred_data += [[] for _ in range(len(true_data) - len(pred_data))]\n", "    \n", "    total_tp = 0\n", "    total_fp = 0\n", "    total_fn = 0\n", "    \n", "    # 计算指标\n", "    for preds, truths in zip(pred_data, true_data):\n", "        pred_set = {tuple(item) for item in preds}\n", "        truth_set = {tuple(item) for item in truths}\n", "        \n", "        # <1>计算当前行的TP/FP/FN\n", "        tp = _____\n", "        fp = _____\n", "        fn = _____\n", "        \n", "        total_tp += tp\n", "        total_fp += fp\n", "        total_fn += fn\n", "    \n", "    # 计算全局指标\n", "    precision = calculate_precision(total_tp, total_fp)\n", "    recall = calculate_recall(total_tp, total_fn)\n", "    f1 = calculate_f1(precision, recall)\n", "    \n", "    # 准备结果字符串\n", "    result_str = f\"全局统计: TP={total_tp} | FP={total_fp} | FN={total_fn}\\n\"\n", "    result_str += f\"精确率(Precision): {precision:.4f}\\n\"\n", "    result_str += f\"召回率(Recall): {recall:.4f}\\n\"\n", "    result_str += f\"F1值: {f1:.4f}\\n\"\n", "    \n", "    # 打印结果\n", "    print(result_str)\n", "\n", "    # 保存结果到文件\n", "    main_result_path = os.path.join('..', 'data', 'main_result.txt')\n", "    with open(main_result_path, 'w', encoding='utf-8') as result_file:\n", "        result_file.write(result_str)\n", "    \n", "    print(\"结果已保存到 main_result.txt\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    result_path = os.path.join('..', 'data', 'result_kg.txt')\n", "    gold_path = os.path.join('..', 'data', 'target.txt')\n", "    main(result_path,gold_path)"]}, {"cell_type": "markdown", "id": "de46b80b", "metadata": {}, "source": ["4.构建知识图谱（base/knowledge_graph.py）"]}, {"cell_type": "code", "execution_count": null, "id": "72afdeee-fcea-45ee-9d4a-85e219a75d78", "metadata": {}, "outputs": [], "source": ["import json\n", "import ast\n", "import networkx as nx\n", "from collections import defaultdict, Counter\n", "\n", "class KnowledgeGraph:\n", "    \"\"\"知识图谱构建和分析类\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.graph = nx.DiGraph()  # 有向图\n", "        self.entity_types = defaultdict(set)  # 实体类型映射\n", "        self.relation_stats = Counter()  # 关系统计\n", "        \n", "    def build_from_triples(self, triples_file_path):\n", "        \"\"\"\n", "        从三元组文件构建知识图谱\n", "        \"\"\"\n", "        try:\n", "            with open(triples_file_path, 'r', encoding='utf-8') as f:\n", "                for line_num, line in enumerate(f, 1):\n", "                    line = line.strip()\n", "                    if not line:\n", "                        continue\n", "                    \n", "                    try:\n", "                        # 解析三元组数据并构建知识图谱\n", "                        try:\n", "                            triples = json.loads(line)  # 解析标准JSON（双引号）\n", "                        except json.JSONDecodeError:\n", "                            triples = ast.literal_eval(line)  # 解析Python格式（单引号）\n", "\n", "                        for triple in triples:\n", "                            if len(triple) >= 3:\n", "                                head, relation, tail = triple[0], triple[1], triple[2]\n", "\n", "                                # <1>添加实体节点和关系边到图中\n", "                                self.graph.add_node(head, type='entity')\n", "                                self.graph.add_node(tail, type='entity')\n", "                                self.graph._____\n", "\n", "                                # 统计关系类型\n", "                                self.relation_stats[relation] += 1\n", "                        \n", "                    except (j<PERSON>.<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SyntaxError):\n", "                        print(f\"第{line_num}行JSON解析错误，跳过\")\n", "                        continue\n", "                        \n", "        except FileNotFoundError:\n", "            print(f\"文件不存在: {triples_file_path}\")\n", "        except Exception as e:\n", "            print(f\"构建知识图谱时出错: {e}\")\n", "    \n", "    def get_entity_neighbors(self, entity, relation_type=None):\n", "        \"\"\"\n", "        获取实体的邻居节点\n", "        \"\"\"\n", "        # 实现基于关系类型的实体邻居查找\n", "        neighbors = []\n", "\n", "        # 获取所有出边邻居（当前实体作为头实体）\n", "        for neighbor in self.graph.successors(entity):\n", "            edge_data = self.graph.get_edge_data(entity, neighbor)\n", "            if relation_type is None or edge_data.get('relation') == relation_type:\n", "                neighbors.append((neighbor, edge_data.get('relation')))\n", "\n", "        # <2>获取所有入边邻居（当前实体作为尾实体）\n", "        for neighbor in self.graph.predecessors(_____):\n", "            edge_data = self.graph.get_edge_data(_____, entity)\n", "            if relation_type is None or edge_data.get('relation') == _____:\n", "                neighbors.append((neighbor, edge_data.get('relation')))\n", "\n", "        return neighbors\n", "    \n", "    def find_shortest_path(self, start_entity, end_entity):\n", "        \"\"\"\n", "        查找两个实体之间的最短路径\n", "        \"\"\"\n", "        try:\n", "            # 实现知识图谱中两实体间的最短路径查找\n", "            if start_entity in self.graph and end_entity in self.graph:\n", "                # <3>使用NetworkX算法查找路径\n", "                path = nx.shortest_path(_____, _____, _____)\n", "                return path\n", "            else:\n", "                return None\n", "        except nx.NetworkXNoPath:\n", "            return None\n", "        except Exception as e:\n", "            print(f\"路径查找出错: {e}\")\n", "            return None\n", "    \n", "    def get_graph_statistics(self):\n", "        \"\"\"\n", "        获取知识图谱统计信息\n", "        \"\"\"\n", "        stats = {\n", "            \"节点数量\": self.graph.number_of_nodes(),\n", "            \"边数量\": self.graph.number_of_edges(),\n", "            \"关系类型数\": len(self.relation_stats),\n", "            \"最常见关系\": self.relation_stats.most_common(5),\n", "            \"平均度数\": sum(dict(self.graph.degree()).values()) / self.graph.number_of_nodes() if self.graph.number_of_nodes() > 0 else 0\n", "        }\n", "        return stats\n", "    \n", "    def save_graph(self, output_path):\n", "        \"\"\"\n", "        保存知识图谱到文件\n", "        \"\"\"\n", "        graph_data = {\n", "            \"nodes\": list(self.graph.nodes(data=True)),\n", "            \"edges\": [(u, v, d) for u, v, d in self.graph.edges(data=True)],\n", "            \"statistics\": self.get_graph_statistics()\n", "        }\n", "        \n", "        with open(output_path, 'w', encoding='utf-8') as f:\n", "            json.dump(graph_data, f, ensure_ascii=False, indent=2)\n", "        \n", "        print(f\"知识图谱已保存到: {output_path}\")\n", "\n", "if __name__ == \"__main__\":\n", "    # 构建知识图谱\n", "    kg = KnowledgeGraph()\n", "    kg.build_from_triples(\"../data/target.txt\")\n", "    \n", "    # 输出统计信息\n", "    stats = kg.get_graph_statistics()\n", "    print(\"知识图谱统计信息:\")\n", "    for key, value in stats.items():\n", "        print(f\"  {key}: {value}\")\n", "    \n", "    # 保存知识图谱\n", "    kg.save_graph(\"knowledge_graph.json\")\n", "    \n", "    # 示例查询\n", "    print(\"\\n示例查询:\")\n", "    neighbors = kg.get_entity_neighbors(\"小细胞肺癌\", \"临床表现\")\n", "    print(f\"小细胞肺癌的临床表现: {neighbors}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}