{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from PIL import Image\n", "import sys\n", "import os\n", "\n", "t2_dir= os.getcwd()\n", "custom_ultralytics_root = os.path.join(t2_dir, 'ultralytics')\n", "sys.path.insert(0, custom_ultralytics_root)\n", "\n", "import numpy as np\n", "from pathlib import Path\n", "from typing import Union\n", "import cv2\n", "from ultralytics import YOLO"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤1.数据增强方法定义\n", "请根据提示实现Mosaic数据增强方法，将“resource/task/t2/ultralytics/ultralytics/data/augment.py”文件中的_mosaic4()函数代码补充完整。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤2.数据预处理方法定义\n", "请根据提示将“resource/task/t2/ultralytics/ultralytics/data/augment.py”文件中的v8_transforms数据预处理函数代码补充完整。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤3.可视化图像数据获取及标注\n", "请根据《平台与设备使用指南》操作设备获取图像并进行标注，本步骤使用场景验证平台和标注平台实现。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤4.模型构建\n", "请根据提示将“resource/task/t2/ultralytics/ultralytics/cfg/models/v8/yolov8.yaml”文件中backbone和head部分代码补充完整。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤5.Distribution Focal Loss函数定义\n", "请根据提示将“resource/task/t2/ultralytics/ultralytics/utils/loss.py”文件中DFLoss部分代码补充完整。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤6.模型训练和预测函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train():\n", "    # <1> 使用预训练模型进行训练，加载weights目录下预训练好的yolov8s.pt模型\n", "    model = ____________\n", "    # <2> 使用medicine数据集训练模型,将epoch数设置为100，输入图像大小设置为640\n", "    model.train(data=____________, epochs=____________, imgsz=____________, amp=False)\n", "# 开始训练\n", "train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict():\n", "    # <3> 加载刚训练好的模型评估新采集数据集，训练好的模型在runs目录下\n", "    model = ____________\n", "    # 使用模型评估函数val评估待评估的测试集\n", "    # <4> 请参考medicine.yaml的形式构造vis.yaml，补全vis.yaml中代码\n", "    metrics = model.val(data=\"vis.yaml\")\n", "    print(metrics.box.map)  # map50-95\n", "    print(metrics.box.map50)  # map50\n", "    print(metrics.box.map75)  # map75\n", "    print(metrics.box.maps)  # 包含每个类别的map50-95列表\n", "\n", "    # Accessing AP75 for each category\n", "    ap75_each_category = metrics.box.maps[:, 5]  # 利用maps矩阵可以得到AP75\n", "    return ap75_each_category\n", "# 输出预测结果\n", "print(predict())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤7.检测结果可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_img():\n", "\t# <1> 加载刚训练好的模型评估新采集数据集，训练好的模型在runs目录下\n", "    model = ____________\n", "    # <2> 使用cv2读取测试图像，要求使用步骤三中采集的样本\n", "    img = ____________\n", "    res = model(img)\n", "    ann = res[0].plot()\n", "    while True:\n", "        cv2.imshow(\"yolo\", ann)\n", "        if cv2.wait<PERSON><PERSON>(1) & 0xFF == ord('q'):\n", "            break\n", "    # <3> 设置保存图片的路径，使用sys读取当前路径\n", "    cur_path = ____________\n", "\n", "    if os.path.exists(cur_path):\n", "        cv2.imwrite(cur_path + \"out.jpg\", ann)\n", "    else:\n", "        os.mkdir(cur_path)\n", "        cv2.imwrite(cur_path + \"out.jpg\", ann)\n", "# 展示可视化测试样本\n", "test_img()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤8.平台展示药品检测结果"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import socket\n", "import time\n", "import cv2\n", "import threading\n", "import numpy as np\n", "# 服务器地址和端口************** **************\n", "SERVER_ADDRESS = '**************' # 替换为服务器的实际 IP 地址\n", "SERVER_PORT = 12343\n", "exit_flag = False\n", "JSON = {}\n", "FRAME = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def receive_messages(client_socket):\n", "\"\"\"接收服务器消息的函数\"\"\"\n", "    global exit_flag, JSON, FRAME\n", "    while not exit_flag:\n", "        header = client_socket.recv(6) # 先接收为字节，不解码\n", "        if header.startswith(b'JSONS:'): # 文本数据标识\n", "            # 读取后续数据并解码\n", "            data_length = int.from_bytes(client_socket.recv(4), 'big')\n", "            json_data = client_socket.recv(data_length).decode('utf-8')\n", "            JSON = json.loads(json_data)\n", "        elif header.startswith(b'FRAME:'): # 图像帧标识\n", "            # 直接处理二进制数据\n", "            frame_size = int.from_bytes(client_socket.recv(4), 'big')\n", "            frame_data = b\"\"\n", "            while len(frame_data) < frame_size:\n", "                frame_data += client_socket.recv(4096)\n", "        # 用OpenCV解码\n", "        FRAME = cv2.imdecode(np.frombuffer(frame_data, dtype=np.uint8), 1)\n", "        cv2.imwrite(\"received_frame.jpg\", FRAME)\n", "    print(\"接收线程已退出\")\n", "\n", "def start_client():\n", "    \"\"\"启动客户端\"\"\"\n", "    client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "    client_socket.connect((SERVER_ADDRESS, SERVER_PORT))\n", "    print(\"连接到服务器\")\n", "    # 创建一个线程用于接收消息\n", "    receive_thread = threading.Thread(target=receive_messages, args=\n", "    (client_socket,))\n", "    receive_thread.daemon = True # 将线程设置为守护线程\n", "    receive_thread.start()\n", "    while True:\n", "        # if FRAME:\n", "        # # cv2.imshow(\"received_frame\", frame)\n", "        # # if cv2.wait<PERSON><PERSON>(10) == ord('q'):\n", "        # # cv2.destroyAllWindows()\n", "        # # exit()\n", "        # cv2.imwrite(\"received_frame.jpg\", FRAME)\n", "        if JSON:\n", "            print(\"接收到json:\", JSON)\n", "            # rgb|02|03 (00~07;00~07), fan|0010 (0000~0020), tub|02|03 (00~07;00~15),\n", "            # st1|1000(0500-2500), st2|1|100(0~1;000~255),\n", "            # han|100|100|255|255|255|255|255(000~255)\n", "            # cmd = \"rgb0506\"\n", "            print(\"rgb|02|03 (00~07;00~07), fan|0010 (0000~0020), tub|02|03 (00~07;00~15),\")\n", "            print(\"st1|1000(0500-2500), st2|1|100(0~1;000~255),\")\n", "            print(\"han|100|100|255|255|255|255|255(000~255)\")\n", "            cmd = input(\"指令：\")\n", "            client_socket.send(cmd.encode('utf-8'))\n", "        time.sleep(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if __name__ == \"__main__\":\n", "    start_client()\n", "    pre = predict()\n", "    if pre >= 0.5:\n", "        # <1> 若评估结果大于等于阈值，认为达成任务，RGB显示为绿色，数码管展示类别的ap75结果，灵巧手比出剪刀\n", "        # rgb第一位绿色\n", "        odr_rgb = ____________\n", "        # 数码管第一第二格显示当前pre值，例：0.63显示为63\n", "        odr_tub = ____________\n", "        # 灵巧手展示剪刀手势\n", "        odr_hand = ____________\n", "    else:\n", "        # <2> 若评估结果小于阈值，认为未达成任务，RGB显示为红色，数码管展示类别的ap75结果，灵巧手比出拳头\n", "        # rgb第一位红色\n", "        odr_rgb = ____________\n", "        # 数码管第一第二格显示当前pre值，例：0.42显示为42\n", "        odr_tub = ____________\n", "        # 灵巧手展示握拳手势\n", "        odr_hand = ____________"]}], "metadata": {"kernelspec": {"display_name": "Python (yolov8)", "language": "python", "name": "yolov8"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 4}