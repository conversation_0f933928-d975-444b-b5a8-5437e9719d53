{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING ⚠️ Ultralytics settings reset to default values. This may be due to a possible problem with your settings or a recent ultralytics package update. \n", "View Ultralytics Settings with 'yolo settings' or at '/home/<USER>/.config/Ultralytics/settings.json'\n", "Update Settings with 'yolo settings key=value', i.e. 'yolo settings runs_dir=path/to/dir'. For help see https://docs.ultralytics.com/quickstart/#ultralytics-settings.\n"]}], "source": ["import torch\n", "from PIL import Image\n", "import sys\n", "import os\n", "\n", "t2_dir= os.getcwd()\n", "custom_ultralytics_root = os.path.join(t2_dir, 'ultralytics')\n", "sys.path.insert(0, custom_ultralytics_root)\n", "\n", "import numpy as np\n", "from pathlib import Path\n", "from typing import Union\n", "import cv2\n", "from ultralytics import YOLO\n", "from ultralytics.engine.model import Model\n", "from typing import Any, Dict, List, Optional, Union\n", "from ultralytics.models import yolo\n", "from ultralytics.nn.tasks import (\n", "    ClassificationModel,\n", "    DetectionModel,\n", "    OBBModel,\n", "    PoseModel,\n", "    SegmentationModel,\n", "    WorldModel,\n", "    YOLOEModel,\n", "    YOLOESegModel,\n", ")\n", "from ultralytics.utils import ROOT, YAML"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'ultralytics.models.yolo.model.YOLO'>\n"]}], "source": ["def train():\n", "    # 使用预训练模型进行训练\n", "    model = YOLO('weights/yolov8s.pt')\n", "    # 使用medicine数据集训练模型\n", "    model.train(data=\"medicine.yaml\", epochs=100, imgsz=640, amp=False)\n", "# 开始训练\n", "train()\n", "print(YOLO)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def test_img():\n", "\t# 训练好的模型权重路径\n", "    model = YOLO(\"runs/detect/train/weights/best.pt\")\n", "    # 测试图片的路径\n", "    img = cv2.imread(\"test.jpg\")\n", "    res = model(img)\n", "    ann = res[0].plot()\n", "    while True:\n", "        cv2.imshow(\"yolo\", ann)\n", "        if cv2.wait<PERSON><PERSON>(1) & 0xFF == ord('q'):\n", "            break\n", "    # 设置保存图片的路径\n", "    cur_path = sys.path[0]\n", "    print(cur_path, sys.path)\n", "\n", "    if os.path.exists(cur_path):\n", "        cv2.imwrite(cur_path + \"out.jpg\", ann)\n", "    else:\n", "        os.mkdir(cur_path)\n", "        cv2.imwrite(cur_path + \"out.jpg\", ann)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def predict():\n", "    # Load a model\n", "    # model = YOLO('yolov8n.pt')  # 加载官方的模型权重作评估\n", "    model = YOLO('runs/detect/train/weights/best.pt')  # 加载自定义的模型权重作评估\n", "\n", "    # 评估\n", "    metrics = model.val()\n", "    # 如果要在新的数据集上测试训练结果，需要将数据集绝对路径传入，例如：\n", "    # metrics = model.val(data=“YOLOv8/.../VOC.yaml”)\n", "    print(metrics.box.map)  # map50-95\n", "    print(metrics.box.map50)  # map50\n", "    print(metrics.box.map75)  # map75\n", "    print(metrics.box.maps)  # 包含每个类别的map50-95列表\n", "\n", "    # Accessing AP75 for each category\n", "    ap75_each_category = metrics.box.maps[:, 5]  # 利用maps矩阵可以得到AP75\n", "    print(ap75_each_category)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ultralytics 8.3.166 🚀 Python-3.8.16 torch-1.13.0+cu116 CUDA:0 (NVIDIA GeForce RTX 4090, 24217MiB)\n", "WARNING ⚠️ Upgrade to torch>=2.0.0 for deterministic training.\n", "\u001b[34m\u001b[1mengine/trainer: \u001b[0magnostic_nms=False, amp=True, augment=False, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=medicine.yaml, degrees=0.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=100, erasing=0.4, exist_ok=False, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=640, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=weights/yolov8s.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=train5, nbs=64, nms=False, opset=None, optimize=False, optimizer=auto, overlap_mask=True, patience=100, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=None, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=runs/detect/train5, save_frames=False, save_json=False, save_period=-1, save_txt=False, scale=0.5, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3.0, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None\n", "medicine.yaml\n", "Overriding model.yaml nc=80 with nc=1\n", "\n", "                   from  n    params  module                                       arguments                     \n", "  0                  -1  1       928  ultralytics.nn.modules.conv.Conv             [3, 32, 3, 2]                 \n", "  1                  -1  1     18560  ultralytics.nn.modules.conv.Conv             [32, 64, 3, 2]                \n", "  2                  -1  1     29056  ultralytics.nn.modules.block.C2f             [64, 64, 1, True]             \n", "  3                  -1  1     73984  ultralytics.nn.modules.conv.Conv             [64, 128, 3, 2]               \n", "  4                  -1  2    197632  ultralytics.nn.modules.block.C2f             [128, 128, 2, True]           \n", "  5                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]              \n", "  6                  -1  2    788480  ultralytics.nn.modules.block.C2f             [256, 256, 2, True]           \n", "  7                  -1  1   1180672  ultralytics.nn.modules.conv.Conv             [256, 512, 3, 2]              \n", "  8                  -1  1   1838080  ultralytics.nn.modules.block.C2f             [512, 512, 1, True]           \n", "  9                  -1  1    656896  ultralytics.nn.modules.block.SPPF            [512, 512, 5]                 \n", " 10                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 11             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 12                  -1  1    591360  ultralytics.nn.modules.block.C2f             [768, 256, 1]                 \n", " 13                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 14             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 15                  -1  1    148224  ultralytics.nn.modules.block.C2f             [384, 128, 1]                 \n", " 16                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]              \n", " 17            [-1, 12]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 18                  -1  1    493056  ultralytics.nn.modules.block.C2f             [384, 256, 1]                 \n", " 19                  -1  1    590336  ultralytics.nn.modules.conv.Conv             [256, 256, 3, 2]              \n", " 20             [-1, 9]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 21                  -1  1   1969152  ultralytics.nn.modules.block.C2f             [768, 512, 1]                 \n", " 22        [15, 18, 21]  1   2116435  ultralytics.nn.modules.head.Detect           [1, [128, 256, 512]]          \n", "Model summary: 129 layers, 11,135,987 parameters, 11,135,971 gradients, 28.6 GFLOPs\n", "\n", "Transferred 349/355 items from pretrained weights\n", "Freezing layer 'model.22.dfl.conv.weight'\n", "\u001b[34m\u001b[1mAMP: \u001b[0mrunning Automatic Mixed Precision (AMP) checks...\n", "Downloading https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n.pt to 'yolo11n.pt'...\n", "WARNING ⚠️ \u001b[34m\u001b[1mAMP: \u001b[0mchecks skipped. Offline and unable to download YOLO11n for AMP checks. Setting 'amp=True'. If you experience zero-mAP or NaN losses you can disable AMP with amp=False.\n", "\u001b[34m\u001b[1mtrain: \u001b[0mFast image access ✅ (ping: 0.0±0.0 ms, read: 168.7±74.2 MB/s, size: 32.4 KB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mtrain: \u001b[0mScanning /home/<USER>/userSpace/studnet/module_B/t2/datasets/medicine/train/labels... 1276 images, 2 backgrounds, 0 corrupt: 100%|██████████| 1276/1276 [00:01<00:00, 986.16it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[34m\u001b[1mtrain: \u001b[0mNew cache created: /home/<USER>/userSpace/studnet/module_B/t2/datasets/medicine/train/labels.cache\n", "WARNING ⚠️ Box and segment counts should be equal, but got len(segments) = 182, len(boxes) = 1277. To resolve this only boxes will be used and all segments will be removed. To avoid this please supply either a detect or segment dataset, not a detect-segment mixed dataset.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"ename": "NameError", "evalue": "name '__________________________________________' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mtrain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      2\u001b[0m test_img()\n\u001b[1;32m      3\u001b[0m predict()\n", "Cell \u001b[0;32mIn[2], line 5\u001b[0m, in \u001b[0;36mtrain\u001b[0;34m()\u001b[0m\n\u001b[1;32m      3\u001b[0m model \u001b[38;5;241m=\u001b[39m YOLO(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mweights/yolov8s.pt\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m      4\u001b[0m \u001b[38;5;66;03m# 使用medicine数据集训练模型\u001b[39;00m\n\u001b[0;32m----> 5\u001b[0m \u001b[43mmodel\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtrain\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmedicine.yaml\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mepochs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m100\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mimgsz\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m640\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/userSpace/studnet/module_B/t2/ultralytics/ultralytics/engine/model.py:799\u001b[0m, in \u001b[0;36mModel.train\u001b[0;34m(self, trainer, **kwargs)\u001b[0m\n\u001b[1;32m    796\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtrainer\u001b[38;5;241m.\u001b[39mmodel\n\u001b[1;32m    798\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtrainer\u001b[38;5;241m.\u001b[39mhub_session \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msession  \u001b[38;5;66;03m# attach optional HUB session\u001b[39;00m\n\u001b[0;32m--> 799\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtrainer\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtrain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    800\u001b[0m \u001b[38;5;66;03m# Update model and cfg after training\u001b[39;00m\n\u001b[1;32m    801\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m RANK \u001b[38;5;129;01min\u001b[39;00m {\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m0\u001b[39m}:\n", "File \u001b[0;32m~/userSpace/studnet/module_B/t2/ultralytics/ultralytics/engine/trainer.py:227\u001b[0m, in \u001b[0;36mBaseTrainer.train\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    224\u001b[0m         ddp_cleanup(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;28mstr\u001b[39m(file))\n\u001b[1;32m    226\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 227\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_do_train\u001b[49m\u001b[43m(\u001b[49m\u001b[43mworld_size\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/userSpace/studnet/module_B/t2/ultralytics/ultralytics/engine/trainer.py:348\u001b[0m, in \u001b[0;36mBaseTrainer._do_train\u001b[0;34m(self, world_size)\u001b[0m\n\u001b[1;32m    346\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m world_size \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m    347\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_setup_ddp(world_size)\n\u001b[0;32m--> 348\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_setup_train\u001b[49m\u001b[43m(\u001b[49m\u001b[43mworld_size\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    350\u001b[0m nb \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtrain_loader)  \u001b[38;5;66;03m# number of batches\u001b[39;00m\n\u001b[1;32m    351\u001b[0m nw \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mmax\u001b[39m(\u001b[38;5;28mround\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39margs\u001b[38;5;241m.\u001b[39mwarmup_epochs \u001b[38;5;241m*\u001b[39m nb), \u001b[38;5;241m100\u001b[39m) \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39margs\u001b[38;5;241m.\u001b[39mwarmup_epochs \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m  \u001b[38;5;66;03m# warmup iterations\u001b[39;00m\n", "File \u001b[0;32m~/userSpace/studnet/module_B/t2/ultralytics/ultralytics/engine/trainer.py:307\u001b[0m, in \u001b[0;36mBaseTrainer._setup_train\u001b[0;34m(self, world_size)\u001b[0m\n\u001b[1;32m    305\u001b[0m \u001b[38;5;66;03m# Dataloaders\u001b[39;00m\n\u001b[1;32m    306\u001b[0m batch_size \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbatch_size \u001b[38;5;241m/\u001b[39m\u001b[38;5;241m/\u001b[39m \u001b[38;5;28mmax\u001b[39m(world_size, \u001b[38;5;241m1\u001b[39m)\n\u001b[0;32m--> 307\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtrain_loader \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_dataloader\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    308\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtrain\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbatch_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbatch_size\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrank\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mLOCAL_RANK\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtrain\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\n\u001b[1;32m    309\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    310\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m RANK \u001b[38;5;129;01min\u001b[39;00m {\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m0\u001b[39m}:\n\u001b[1;32m    311\u001b[0m     \u001b[38;5;66;03m# Note: When training DOTA dataset, double batch size could get OOM on images with >2000 objects.\u001b[39;00m\n\u001b[1;32m    312\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtest_loader \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_dataloader(\n\u001b[1;32m    313\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdata\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mval\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdata\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtest\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[1;32m    314\u001b[0m         batch_size\u001b[38;5;241m=\u001b[39mbatch_size \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39margs\u001b[38;5;241m.\u001b[39mtask \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mobb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m batch_size \u001b[38;5;241m*\u001b[39m \u001b[38;5;241m2\u001b[39m,\n\u001b[1;32m    315\u001b[0m         rank\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m,\n\u001b[1;32m    316\u001b[0m         mode\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mval\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    317\u001b[0m     )\n", "File \u001b[0;32m~/userSpace/studnet/module_B/t2/ultralytics/ultralytics/models/yolo/detect/train.py:84\u001b[0m, in \u001b[0;36mDetectionTrainer.get_dataloader\u001b[0;34m(self, dataset_path, batch_size, rank, mode)\u001b[0m\n\u001b[1;32m     82\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m mode \u001b[38;5;129;01min\u001b[39;00m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtrain\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mval\u001b[39m\u001b[38;5;124m\"\u001b[39m}, \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMode must be \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtrain\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m or \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mval\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, not \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmode\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     83\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m torch_distributed_zero_first(rank):  \u001b[38;5;66;03m# init dataset *.cache only once if DDP\u001b[39;00m\n\u001b[0;32m---> 84\u001b[0m     dataset \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbuild_dataset\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdataset_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbatch_size\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     85\u001b[0m shuffle \u001b[38;5;241m=\u001b[39m mode \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtrain\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     86\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(dataset, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrect\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mFalse\u001b[39;00m) \u001b[38;5;129;01mand\u001b[39;00m shuffle:\n", "File \u001b[0;32m~/userSpace/studnet/module_B/t2/ultralytics/ultralytics/models/yolo/detect/train.py:67\u001b[0m, in \u001b[0;36mDetectionTrainer.build_dataset\u001b[0;34m(self, img_path, mode, batch)\u001b[0m\n\u001b[1;32m     55\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m     56\u001b[0m \u001b[38;5;124;03mBuild YOLO Dataset for training or validation.\u001b[39;00m\n\u001b[1;32m     57\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     64\u001b[0m \u001b[38;5;124;03m    (Dataset): YOLO dataset object configured for the specified mode.\u001b[39;00m\n\u001b[1;32m     65\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m     66\u001b[0m gs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mmax\u001b[39m(\u001b[38;5;28mint\u001b[39m(de_parallel(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel)\u001b[38;5;241m.\u001b[39mstride\u001b[38;5;241m.\u001b[39mmax() \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;241m0\u001b[39m), \u001b[38;5;241m32\u001b[39m)\n\u001b[0;32m---> 67\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mbuild_yolo_dataset\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mimg_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbatch\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m==\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mval\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstride\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mgs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/userSpace/studnet/module_B/t2/ultralytics/ultralytics/data/build.py:127\u001b[0m, in \u001b[0;36mbuild_yolo_dataset\u001b[0;34m(cfg, img_path, batch, data, mode, rect, stride, multi_modal)\u001b[0m\n\u001b[1;32m    125\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Build and return a YOLO dataset based on configuration parameters.\"\"\"\u001b[39;00m\n\u001b[1;32m    126\u001b[0m dataset \u001b[38;5;241m=\u001b[39m YOLOMultiModalDataset \u001b[38;5;28;01mif\u001b[39;00m multi_modal \u001b[38;5;28;01melse\u001b[39;00m YOLODataset\n\u001b[0;32m--> 127\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdataset\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    128\u001b[0m \u001b[43m    \u001b[49m\u001b[43mimg_path\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mimg_path\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    129\u001b[0m \u001b[43m    \u001b[49m\u001b[43mimgsz\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcfg\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mimgsz\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    130\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbatch_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbatch\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    131\u001b[0m \u001b[43m    \u001b[49m\u001b[43maugment\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m==\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtrain\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# augmentation\u001b[39;49;00m\n\u001b[1;32m    132\u001b[0m \u001b[43m    \u001b[49m\u001b[43mhyp\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcfg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# TODO: probably add a get_hyps_from_cfg function\u001b[39;49;00m\n\u001b[1;32m    133\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcfg\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrect\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrect\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# rectangular batches\u001b[39;49;00m\n\u001b[1;32m    134\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcache\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcfg\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcache\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    135\u001b[0m \u001b[43m    \u001b[49m\u001b[43msingle_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcfg\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msingle_cls\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    136\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstride\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mint\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mstride\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    137\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpad\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.0\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m==\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtrain\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;241;43m0.5\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    138\u001b[0m \u001b[43m    \u001b[49m\u001b[43mprefix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcolorstr\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43mf\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mmode\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m: \u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    139\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcfg\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    140\u001b[0m \u001b[43m    \u001b[49m\u001b[43mclasses\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcfg\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclasses\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    141\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    142\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfraction\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcfg\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfraction\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m==\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtrain\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;241;43m1.0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    143\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/userSpace/studnet/module_B/t2/ultralytics/ultralytics/data/dataset.py:88\u001b[0m, in \u001b[0;36mYOLODataset.__init__\u001b[0;34m(self, data, task, *args, **kwargs)\u001b[0m\n\u001b[1;32m     86\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdata \u001b[38;5;241m=\u001b[39m data\n\u001b[1;32m     87\u001b[0m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39muse_segments \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39muse_keypoints), \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCan not use both segments and keypoints.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m---> 88\u001b[0m \u001b[38;5;28;43ms<PERSON>r\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mchannels\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mchannels\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/userSpace/studnet/module_B/t2/ultralytics/ultralytics/data/base.py:146\u001b[0m, in \u001b[0;36mBaseDataset.__init__\u001b[0;34m(self, img_path, imgsz, cache, augment, hyp, prefix, rect, batch_size, stride, pad, single_cls, classes, fraction, channels)\u001b[0m\n\u001b[1;32m    143\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcache_images()\n\u001b[1;32m    145\u001b[0m \u001b[38;5;66;03m# Transforms\u001b[39;00m\n\u001b[0;32m--> 146\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtransforms \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbuild_transforms\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhyp\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhyp\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/userSpace/studnet/module_B/t2/ultralytics/ultralytics/data/dataset.py:222\u001b[0m, in \u001b[0;36mYOLODataset.build_transforms\u001b[0;34m(self, hyp)\u001b[0m\n\u001b[1;32m    220\u001b[0m     hyp\u001b[38;5;241m.\u001b[39mmixup \u001b[38;5;241m=\u001b[39m hyp\u001b[38;5;241m.\u001b[39mmixup \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maugment \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrect \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;241m0.0\u001b[39m\n\u001b[1;32m    221\u001b[0m     hyp\u001b[38;5;241m.\u001b[39mcutmix \u001b[38;5;241m=\u001b[39m hyp\u001b[38;5;241m.\u001b[39mcutmix \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maugment \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrect \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;241m0.0\u001b[39m\n\u001b[0;32m--> 222\u001b[0m     transforms \u001b[38;5;241m=\u001b[39m \u001b[43mv8_transforms\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mimgsz\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mhyp\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    223\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    224\u001b[0m     transforms \u001b[38;5;241m=\u001b[39m Compose([LetterBox(new_shape\u001b[38;5;241m=\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mimgsz, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mimgsz), scaleup\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)])\n", "File \u001b[0;32m~/userSpace/studnet/module_B/t2/ultralytics/ultralytics/data/augment.py:2574\u001b[0m, in \u001b[0;36mv8_transforms\u001b[0;34m(dataset, imgsz, hyp, stretch)\u001b[0m\n\u001b[1;32m   2565\u001b[0m affine \u001b[38;5;241m=\u001b[39m RandomPerspective(\n\u001b[1;32m   2566\u001b[0m     degrees\u001b[38;5;241m=\u001b[39mhyp\u001b[38;5;241m.\u001b[39mdegrees,\n\u001b[1;32m   2567\u001b[0m     translate\u001b[38;5;241m=\u001b[39mhyp\u001b[38;5;241m.\u001b[39mtranslate,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   2571\u001b[0m     pre_transform\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01mif\u001b[39;00m stretch \u001b[38;5;28;01melse\u001b[39;00m LetterBox(new_shape\u001b[38;5;241m=\u001b[39m(imgsz, imgsz)),\n\u001b[1;32m   2572\u001b[0m )\n\u001b[1;32m   2573\u001b[0m \u001b[38;5;66;03m# <1> 使用Compose函数将mosaic和随机扰动整合成前处理pre_transform\u001b[39;00m\n\u001b[0;32m-> 2574\u001b[0m pre_transform \u001b[38;5;241m=\u001b[39m \u001b[43m__________________________________________\u001b[49m\n\u001b[1;32m   2575\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m hyp\u001b[38;5;241m.\u001b[39mcopy_paste_mode \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mflip\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m   2576\u001b[0m     pre_transform\u001b[38;5;241m.\u001b[39minsert(\u001b[38;5;241m1\u001b[39m, CopyPaste(p\u001b[38;5;241m=\u001b[39mhyp\u001b[38;5;241m.\u001b[39mcopy_paste, mode\u001b[38;5;241m=\u001b[39mhyp\u001b[38;5;241m.\u001b[39mcopy_paste_mode))\n", "\u001b[0;31mNameError\u001b[0m: name '__________________________________________' is not defined"]}], "source": ["train()\n", "test_img()\n", "predict()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import socket\n", "import time\n", "import cv2\n", "import threading\n", "import numpy as np\n", "# 服务器地址和端口************** **************\n", "SERVER_ADDRESS = '**************' # 替换为服务器的实际 IP 地址\n", "SERVER_PORT = 12343\n", "exit_flag = False\n", "JSON = {}\n", "FRAME = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def receive_messages(client_socket):\n", "    \"\"\"接收服务器消息的函数\"\"\"\n", "    global exit_flag, JSON, FRAME\n", "    while not exit_flag:\n", "        header = client_socket.recv(6) # 先接收为字节，不解码\n", "        if header.startswith(b'JSONS:'): # 文本数据标识\n", "            # 读取后续数据并解码\n", "            data_length = int.from_bytes(client_socket.recv(4), 'big')\n", "            json_data = client_socket.recv(data_length).decode('utf-8')\n", "            JSON = json.loads(json_data)\n", "        elif header.startswith(b'FRAME:'): # 图像帧标识\n", "            # 直接处理二进制数据\n", "            frame_size = int.from_bytes(client_socket.recv(4), 'big')\n", "            frame_data = b\"\"\n", "            while len(frame_data) < frame_size:\n", "                frame_data += client_socket.recv(4096)\n", "        # 用OpenCV解码\n", "        FRAME = cv2.imdecode(np.frombuffer(frame_data, dtype=np.uint8), 1)\n", "        cv2.imwrite(\"received_frame.jpg\", FRAME)\n", "    print(\"接收线程已退出\")\n", "\n", "def start_client():\n", "    \"\"\"启动客户端\"\"\"\n", "    client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "    client_socket.connect((SERVER_ADDRESS, SERVER_PORT))\n", "    print(\"连接到服务器\")\n", "    # 创建一个线程用于接收消息\n", "    receive_thread = threading.Thread(target=receive_messages, args=\n", "    (client_socket,))\n", "    receive_thread.daemon = True # 将线程设置为守护线程\n", "    receive_thread.start()\n", "    while True:\n", "        # if FRAME:\n", "        # # cv2.imshow(\"received_frame\", frame)\n", "        # # if cv2.wait<PERSON><PERSON>(10) == ord('q'):\n", "        # # cv2.destroyAllWindows()\n", "        # # exit()\n", "        # cv2.imwrite(\"received_frame.jpg\", FRAME)\n", "        if JSON:\n", "            print(\"接收到json:\", JSON)\n", "            # rgb|02|03 (00~07;00~07), fan|0010 (0000~0020), tub|02|03 (00~07;00~15),\n", "            # st1|1000(0500-2500), st2|1|100(0~1;000~255),\n", "            # han|100|100|255|255|255|255|255(000~255)\n", "            # cmd = \"rgb0506\"\n", "            print(\"rgb|02|03 (00~07;00~07), fan|0010 (0000~0020), tub|02|03 (00~07;00~15),\")\n", "            print(\"st1|1000(0500-2500), st2|1|100(0~1;000~255),\")\n", "            print(\"han|100|100|255|255|255|255|255(000~255)\")\n", "            cmd = input(\"指令：\")\n", "            client_socket.send(cmd.encode('utf-8'))\n", "        time.sleep(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if __name__ == \"__main__\":\n", "    start_client()\n", "    pre = predict()\n", "    if pre >= 0.5:\n", "        # <1> 若评估结果大于等于阈值，认为达成任务，RGB显示为绿色，数码管展示类别的ap75结果，灵巧手比出剪刀\n", "        # rgb第一位绿色\n", "        odr_rgb = ____________\n", "        # 数码管第一第二格显示当前pre值，例：0.63显示为63\n", "        odr_tub = ____________\n", "        # 灵巧手展示剪刀手势\n", "        odr_hand = ____________\n", "    else:\n", "        # <2> 若评估结果小于阈值，认为未达成任务，RGB显示为红色，数码管展示类别的ap75结果，灵巧手比出拳头\n", "        # rgb第一位红色\n", "        odr_rgb = ____________\n", "        # 数码管第一第二格显示当前pre值，例：0.42显示为42\n", "        odr_tub = ____________\n", "        # 灵巧手展示握拳手势\n", "        odr_hand = ____________"]}], "metadata": {"kernelspec": {"display_name": "module_B_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 4}