from modelscope import AutoModelFor<PERSON>ausalLM, AutoTokenizer
import torch
import ast
import os
from tqdm import tqdm

class QwenModel:
    def __init__(self):
        """
        初始化Qwen模型
        参数：
            device: 计算设备 (cuda/cpu)
            torch_dtype: 张量精度
            model_path: 模型所在路径(默认Qwen3-0.6B,可修改为Qwen3-4B)
        """
        self.device = "cuda"
        self.torch_dtype = torch.bfloat16
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.model_path = os.path.abspath(os.path.join(script_dir, "../../Qwen3-0.6B"))

        # 延迟加载组件
        self._model = None
        self._tokenizer = None

    @property
    def tokenizer(self):
        if self._tokenizer is None:
            self._tokenizer = AutoTokenizer.from_pretrained(self.model_path)
        return self._tokenizer
    
    @property
    def model(self):
        if self._model is None:
            #------------------------------------------------------1---------------------------------------------
            self._model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
            ).to(self.torch_dtype).to(self.device).eval()
            #------------------------------------------------------1---------------------------------------------
        return self._model

    def extract_triples(self, user_input):
        try:
            # 构造few shot示例
            #------------------------------------------------------2---------------------------------------------
            few_shot = {"user input text": "小细胞肺癌@可有特征性的咳嗽、呼吸困难、咯血、体重减轻、发热、关节痛、皮肤病变、盗汗，或者无症状。",
                        "output triples": '''[["小细胞肺癌", "临床表现", "咳嗽"], ["小细胞肺癌", "临床表现", "呼吸困难"], ["小细胞肺癌", "临床表现", "咯血"], ["小细胞肺癌", "临床表现", "体重减轻"], ["小细胞肺癌", "临床表现", "发热"], ["小细胞肺癌", "临床表现", "关节痛"], ["小细胞肺癌", "临床表现", "皮肤病变"], ["小细胞肺癌", "临床表现", "盗汗"]]'''}
            #------------------------------------------------------2---------------------------------------------
            # 构造prompt
            #------------------------------------------------------3---------------------------------------------
            messages = [
                {"role": "system",
                "content": "你是一个从文本中提取三元组的助手。"
                            "[注意]提取的三元组数不得超过8。"
                            "输出格式应严格遵循以下示例：: [['头实体1', '关系1', '尾实体1'], ['头实体2', '关系2', '尾实体2']]"
                            "仅返回output triples对应的数组"
                            "如下是一个参考示例: "+str(few_shot)
                            }
            ]
            #------------------------------------------------------3---------------------------------------------
            # 测试三元组提取示例
            messages.append({"role": "user", "content": "user input text:" + user_input})

            # 生成模型输入
            text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True,
            enable_thinking=False
            )
            
            # 生成参数设置
            model_inputs = self.tokenizer([text], return_tensors="pt").to(self.model.device)

            # 三元组提取
            #------------------------------------------------------4---------------------------------------------
            generated_ids = self.model.generate(
                **model_inputs,
                max_new_tokens=512,
                temperature=0.1,
            )
            #------------------------------------------------------4---------------------------------------------
            
            output_ids = generated_ids[0][len(model_inputs.input_ids[0]):].tolist() 

            try:
                index = len(output_ids) - output_ids[::-1].index(151668)
            except ValueError:
                index = 0

            assistant_response = self.tokenizer.decode(output_ids[index:], skip_special_tokens=True).strip("\n")

            print(f"*** 输出结果: {assistant_response}")
            return assistant_response

        finally:
            if 'model_inputs' in locals():
                del model_inputs
            if 'generated_ids' in locals():
                del generated_ids
            torch.cuda.empty_cache()
    

if __name__ == "__main__":

    text_path = os.path.join('..', 'data', 'source.txt')
    kg_path = os.path.join('..', 'data', 'target.txt')
    result_path = os.path.join('..', 'data', 'result_kg.txt')

    with open(text_path, 'r', encoding='utf-8') as file:
        text = [line.strip() for line in file]
    
    with open(kg_path, 'r', encoding='utf-8') as file:
        kg = [ast.literal_eval(line.strip()) for line in file]

    print(f"webnlg.txt文本个数为：{len(text)}, webnlg_kg.txt三元组个数为：{len(kg)}")

    model = QwenModel()
    # 打开 result_kg.txt 文件用于写入
    with open(result_path, 'w', encoding='utf-8') as result_file:
        # 知识图谱构建
        for i in tqdm(range(len(text)), desc="调用qwen3提取三元组:"):
            #------------------------------------------------------5---------------------------------------------
            triples = model.extract_triples(text[i])  
            result_file.write(triples + '\n')  # 写入文件并换行
            #------------------------------------------------------5---------------------------------------------
